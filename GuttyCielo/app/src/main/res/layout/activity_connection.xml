<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        >

        <View
            android:id="@+id/ivLogo"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:src="@drawable/ic_launcher_foreground"
            app:layout_constraintBottom_toTopOf="@+id/tvTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="spread_inside" />

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="GUTTY"
            android:textColor="@color/black"
            android:textSize="35sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tvSubtitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivLogo" />

        <TextView
            android:id="@+id/tvSubtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            app:layout_constraintBottom_toTopOf="@+id/cardConnection"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardConnection"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintBottom_toTopOf="@+id/buttonContainer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvSubtitle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/light_blue"
                android:orientation="vertical"
                android:padding="20dp"

                android:textColorHint="@color/white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="Configuração do Sistema"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="URL do Servidor PHP"
                    android:textColorHint="@color/white"
                    app:boxStrokeColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etServerUrl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textUri"
                        android:text=""
                        android:textColor="@color/white" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="Host do Banco PostgreSQL"
                    android:textColorHint="@color/white"
                    app:boxStrokeColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etHost"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textUri"
                        android:textColor="@color/white" />

                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:hint="Porta"
                        android:textColorHint="@color/white"
                        app:boxStrokeColor="@color/white">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etPort"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:textColor="@color/white" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="2"
                        android:hint="Nome do Banco"
                        android:textColorHint="@color/white"
                        app:boxStrokeColor="@color/black">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etDatabase"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:textColor="@color/white" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="Usuário"
                    android:textColorHint="@color/white"
                    app:boxStrokeColor="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etUsername"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"

                        android:textColor="@color/white" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="Senha"
                    android:textColorHint="@color/white"
                    app:boxStrokeColor="@color/black"
                    app:endIconMode="password_toggle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword"

                        android:textColor="@color/white" />

                </com.google.android.material.textfield.TextInputLayout>



            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <LinearLayout
            android:id="@+id/buttonContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cardConnection">


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnConnect"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Conectar ao Banco"
                android:textSize="16sp"
                app:cornerRadius="8dp" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnShowLogs"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="56dp"
                android:layout_marginStart="8dp"
                android:text="Ver Logs"
                android:textSize="14sp"
                app:cornerRadius="8dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvConnectionStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text=""
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/buttonContainer" />

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/buttonContainer"
            app:layout_constraintEnd_toEndOf="@+id/buttonContainer"
            app:layout_constraintStart_toStartOf="@+id/buttonContainer"
            app:layout_constraintTop_toTopOf="@+id/buttonContainer" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>

package com.example.guttycielo.activities

import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.guttycielo.adapters.MergeRecenteAdapter
import com.example.guttycielo.databinding.ActivityMergeComandasBinding
import com.example.guttycielo.models.MergeLog
import com.example.guttycielo.network.DatabaseManager
import kotlinx.coroutines.launch

/**
 * Activity para unir comandas e gerenciar merges recentes
 */
class MergeComandasActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMergeComandasBinding
    private lateinit var mergeAdapter: MergeRecenteAdapter
    private val mergesRecentes = mutableListOf<MergeLog>()

    companion object {
        private const val TAG = "MergeComandasActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMergeComandasBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupRecyclerView()
        setupButtons()
        loadMergesRecentes()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }

    private fun setupRecyclerView() {
        mergeAdapter = MergeRecenteAdapter(
            merges = mergesRecentes,
            onDesfazerClick = { merge -> desfazerMerge(merge) }
        )
        
        binding.rvMergesRecentes.apply {
            layoutManager = LinearLayoutManager(this@MergeComandasActivity)
            adapter = mergeAdapter
        }
    }

    private fun setupButtons() {
        binding.btnVisualizar.setOnClickListener {
            visualizarComandas()
        }

        binding.btnUnir.setOnClickListener {
            confirmarUnirComandas()
        }

        // Botões para buscar comandas
        binding.btnBuscarOrigem.setOnClickListener {
            mostrarComandasAbertas { comanda ->
                binding.etComandaOrigem.setText(comanda.getNumeroComanda())
            }
        }

        binding.btnBuscarDestino.setOnClickListener {
            mostrarComandasAbertas { comanda ->
                binding.etComandaDestino.setText(comanda.getNumeroComanda())
            }
        }
    }

    private fun visualizarComandas() {
        val origem = binding.etComandaOrigem.text.toString().trim()
        val destino = binding.etComandaDestino.text.toString().trim()

        if (origem.isEmpty() || destino.isEmpty()) {
            Toast.makeText(this, "Preencha origem e destino", Toast.LENGTH_SHORT).show()
            return
        }

        if (origem == destino) {
            Toast.makeText(this, "Origem e destino não podem ser iguais", Toast.LENGTH_SHORT).show()
            return
        }

        // TODO: Implementar visualização das comandas
        Toast.makeText(this, "Visualizando comandas $origem → $destino", Toast.LENGTH_SHORT).show()
    }

    private fun confirmarUnirComandas() {
        val origem = binding.etComandaOrigem.text.toString().trim()
        val destino = binding.etComandaDestino.text.toString().trim()

        if (origem.isEmpty() || destino.isEmpty()) {
            Toast.makeText(this, "Preencha origem e destino", Toast.LENGTH_SHORT).show()
            return
        }

        if (origem == destino) {
            Toast.makeText(this, "Origem e destino não podem ser iguais", Toast.LENGTH_SHORT).show()
            return
        }

        // Confirma a operação
        AlertDialog.Builder(this)
            .setTitle("Confirmar União")
            .setMessage("Deseja unir a comanda $origem com a comanda $destino?\n\nTodos os itens da comanda $origem serão movidos para a comanda $destino.")
            .setPositiveButton("UNIR") { _, _ ->
                unirComandas(origem, destino)
            }
            .setNegativeButton("CANCELAR", null)
            .show()
    }

    private fun unirComandas(origem: String, destino: String) {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "Iniciando merge: $origem → $destino")
                
                val sucesso = DatabaseManager.mergeComandas(origem, destino)
                
                runOnUiThread {
                    if (sucesso) {
                        Toast.makeText(this@MergeComandasActivity, "Comandas unidas com sucesso!", Toast.LENGTH_SHORT).show()
                        
                        // Limpa os campos
                        binding.etComandaOrigem.text?.clear()
                        binding.etComandaDestino.text?.clear()
                        
                        // Recarrega merges recentes
                        loadMergesRecentes()
                    } else {
                        Toast.makeText(this@MergeComandasActivity, "Erro ao unir comandas", Toast.LENGTH_LONG).show()
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao unir comandas: ${e.message}", e)
                runOnUiThread {
                    Toast.makeText(this@MergeComandasActivity, "Erro: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun desfazerMerge(merge: MergeLog) {
        AlertDialog.Builder(this)
            .setTitle("Desfazer Merge")
            .setMessage("Deseja desfazer o merge ${merge.getDescricao()}?\n\nOs itens serão movidos de volta para a comanda original.")
            .setPositiveButton("DESFAZER") { _, _ ->
                executarDesfazerMerge(merge)
            }
            .setNegativeButton("CANCELAR", null)
            .show()
    }

    private fun executarDesfazerMerge(merge: MergeLog) {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "Desfazendo merge: ${merge.id}")
                
                val sucesso = DatabaseManager.desfazerMerge(merge.id)
                
                runOnUiThread {
                    if (sucesso) {
                        Toast.makeText(this@MergeComandasActivity, "Merge desfeito com sucesso!", Toast.LENGTH_SHORT).show()
                        loadMergesRecentes()
                    } else {
                        Toast.makeText(this@MergeComandasActivity, "Erro ao desfazer merge", Toast.LENGTH_LONG).show()
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao desfazer merge: ${e.message}", e)
                runOnUiThread {
                    Toast.makeText(this@MergeComandasActivity, "Erro: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun loadMergesRecentes() {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "Carregando merges recentes")
                
                val merges = DatabaseManager.getMergesRecentes()
                
                runOnUiThread {
                    mergesRecentes.clear()
                    mergesRecentes.addAll(merges)
                    mergeAdapter.notifyDataSetChanged()
                    
                    // Mostra/esconde mensagem de vazio
                    if (merges.isEmpty()) {
                        binding.tvNoMerges.visibility = android.view.View.VISIBLE
                        binding.rvMergesRecentes.visibility = android.view.View.GONE
                    } else {
                        binding.tvNoMerges.visibility = android.view.View.GONE
                        binding.rvMergesRecentes.visibility = android.view.View.VISIBLE
                    }
                    
                    Log.d(TAG, "Carregados ${merges.size} merges recentes")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao carregar merges recentes: ${e.message}", e)
                runOnUiThread {
                    Toast.makeText(this@MergeComandasActivity, "Erro ao carregar histórico: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun mostrarComandasAbertas(onComandaSelected: (com.example.guttycielo.models.ComandaAberta) -> Unit) {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "Buscando comandas abertas")

                val comandas = DatabaseManager.getComandasAbertas()

                runOnUiThread {
                    if (comandas.isEmpty()) {
                        Toast.makeText(this@MergeComandasActivity, "Nenhuma comanda aberta encontrada", Toast.LENGTH_SHORT).show()
                        return@runOnUiThread
                    }

                    // Cria lista de opções para o dialog
                    val opcoes = comandas.map { it.getDescricaoFormatada() }.toTypedArray()

                    AlertDialog.Builder(this@MergeComandasActivity)
                        .setTitle("Selecionar Comanda")
                        .setItems(opcoes) { _, which ->
                            val comandaSelecionada = comandas[which]
                            onComandaSelected(comandaSelecionada)
                        }
                        .setNegativeButton("Cancelar", null)
                        .show()
                }

            } catch (e: Exception) {
                Log.e(TAG, "Erro ao buscar comandas abertas: ${e.message}", e)
                runOnUiThread {
                    Toast.makeText(this@MergeComandasActivity, "Erro ao buscar comandas: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
}

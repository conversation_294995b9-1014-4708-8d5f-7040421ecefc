package com.example.guttycielo.models

import java.io.Serializable

/**
 * Modelo para representar um pedido em aberto (impresso = 0)
 * Agrupa todos os itens de uma mesa/comanda específica
 */
data class PendingOrder(
    val customerName: String,
    val tableNumber: String,
    val totalItems: Int,
    val totalAmount: Double,
    val items: List<PendingOrderItem>
) : Serializable

/**
 * Modelo para representar um item individual do pedido
 */
data class PendingOrderItem(
    val id: Int,
    val productCode: String,
    val productName: String,
    val quantity: Int,
    val unitPrice: Double,
    val totalPrice: Double,
    val gtin: String? = null
) : Serializable

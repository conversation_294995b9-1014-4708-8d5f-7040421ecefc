package com.example.guttycielo.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import com.example.guttycielo.databinding.DialogQuantityBinding
import com.example.guttycielo.models.Product
import java.text.NumberFormat
import java.util.Locale

class QuantityDialog(
    context: Context,
    private val product: Product,
    private val onAddToCart: (Product, Int) -> Unit
) : Dialog(context) {

    private lateinit var binding: DialogQuantityBinding
    private var quantity = 1

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogQuantityBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Configura a largura do modal
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.9).toInt(), // 90% da largura da tela
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )

        setupViews()
        updateTotal()
    }

    private fun setupViews() {
        binding.apply {
            tvProductName.text = product.descricao
            
            val currencyFormat = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
            tvPrice.text = currencyFormat.format(product.precoVenda)

            btnMinus.setOnClickListener {
                if (quantity > 1) {
                    quantity--
                    updateQuantity()
                }
            }

            btnPlus.setOnClickListener {
                quantity++
                updateQuantity()
            }

            btnCancel.setOnClickListener {
                dismiss()
            }

            btnAdd.setOnClickListener {
                onAddToCart(product, quantity)
                dismiss()
            }
        }
    }

    private fun updateQuantity() {
        binding.tvQuantity.text = quantity.toString()
        updateTotal()
    }

    private fun updateTotal() {
        val total = product.precoVenda * quantity
        val currencyFormat = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
        binding.tvTotal.text = "Total: ${currencyFormat.format(total)}"
    }
}

<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="32dp">

        <!-- Logo/Título -->
        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Gutty - Sistema PDV"
            android:textColor="@color/light_blue"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvSubtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Identificação do Vendedor"
            android:textColor="@color/gray"
            android:textSize="16sp"
            android:layout_marginBottom="48dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <!-- Card de Login -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardLogin"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvSubtitle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Código do Vendedor"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    app:boxStrokeColor="@color/light_blue"
                    app:hintTextColor="@color/light_blue">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etCodigoVendedor"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Digite o código do vendedor"
                        android:inputType="number"
                        android:maxLength="10"
                        android:textSize="18sp"
                        android:textAlignment="center" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnLogin"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="ENTRAR"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/light_blue"
                    app:cornerRadius="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/cardLogin"
            app:layout_constraintEnd_toEndOf="@+id/cardLogin"
            app:layout_constraintStart_toStartOf="@+id/cardLogin"
            app:layout_constraintTop_toTopOf="@+id/cardLogin" />

        <!-- Informações do Sistema -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:orientation="vertical"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/cardLogin"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cielo Smart Compatible"
                android:textColor="@color/gray"
                android:textSize="12sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Versão 1.5"
                android:textColor="@color/gray"
                android:textSize="10sp"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>

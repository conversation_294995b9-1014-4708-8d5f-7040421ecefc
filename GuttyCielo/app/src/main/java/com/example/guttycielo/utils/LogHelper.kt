package com.example.guttycielo.utils

import android.util.Log

object LogHelper {
    private const val TAG = "GuttyCielo"
    private val logs = mutableListOf<String>()
    
    fun d(message: String) {
        val logMessage = "[DEBUG] $message"
        Log.d(TAG, message)
        addToInternalLog(logMessage)
    }
    
    fun e(message: String, throwable: Throwable? = null) {
        val logMessage = "[ERROR] $message"
        Log.e(TAG, message, throwable)
        addToInternalLog(logMessage)
        throwable?.let {
            addToInternalLog("[ERROR] Stack trace: ${it.stackTraceToString()}")
        }
    }
    
    fun i(message: String) {
        val logMessage = "[INFO] $message"
        Log.i(TAG, message)
        addToInternalLog(logMessage)
    }
    
    private fun addToInternalLog(message: String) {
        synchronized(logs) {
            logs.add("${System.currentTimeMillis()}: $message")
            // Manter apenas os últimos 100 logs
            if (logs.size > 100) {
                logs.removeAt(0)
            }
        }
    }
    
    fun getAllLogs(): List<String> {
        return synchronized(logs) {
            logs.toList()
        }
    }
    
    fun clearLogs() {
        synchronized(logs) {
            logs.clear()
        }
    }
}

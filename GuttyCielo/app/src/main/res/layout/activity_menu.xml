<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!-- Header com info do vendedor -->
    <LinearLayout
        android:id="@+id/headerLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/light_blue"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvVendedorInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Olá, [Nome do Vendedor]"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Gutty - Sistema PDV"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:alpha="0.8"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"

                >

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnConfig"
                    android:text="⚙️"

                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:textSize="10sp"
                    android:textColor="@color/light_blue"
                    app:backgroundTint="@color/white"
                    app:cornerRadius="18dp"
                    android:minWidth="60dp"
                    android:layout_marginRight="10dp"

                    />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnLogout"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="SAIR"
                    android:textSize="10sp"
                    android:textColor="@color/light_blue"
                    app:backgroundTint="@color/white"
                    app:cornerRadius="18dp"
                    android:minWidth="60dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerLayout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Primeira linha: Anotar Pedido + Receber Pagamento -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <!-- Card Anotar Pedido -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardAnotarPedido"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="6dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="3dp"
                    app:cardBackgroundColor="#2196F3"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_edit"
                            android:layout_marginBottom="4dp"
                            app:tint="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="ANOTAR\nPEDIDO"
                            android:textColor="@color/white"
                            android:textSize="11sp"
                            android:textStyle="bold"
                            android:textAlignment="center"
                            android:lineSpacingExtra="1dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Card Receber Pagamento -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardReceberPagamento"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_weight="1"
                    android:layout_marginStart="6dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="3dp"
                    app:cardBackgroundColor="#4CAF50"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_payment"
                            android:layout_marginBottom="4dp"
                            app:tint="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="RECEBER\nPAGAMENTO"
                            android:textColor="@color/white"
                            android:textSize="11sp"
                            android:textStyle="bold"
                            android:textAlignment="center"
                            android:lineSpacingExtra="1dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Segunda linha: Ver Comandas + Pedidos Pendentes -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- Card Ver Comandas -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardVerComandas"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="6dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="3dp"
                    app:cardBackgroundColor="#FF9800"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_list"
                            android:layout_marginBottom="4dp"
                            app:tint="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="VER\nCOMANDAS"
                            android:textColor="@color/white"
                            android:textSize="11sp"
                            android:textStyle="bold"
                            android:textAlignment="center"
                            android:lineSpacingExtra="1dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Card Pedidos Pendentes -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardPedidosPendentes"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_weight="1"
                    android:layout_marginStart="6dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="3dp"
                    app:cardBackgroundColor="#F44336"
                    app:rippleColor="?attr/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_pending"
                            android:layout_marginBottom="4dp"
                            app:tint="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="PEDIDOS\nPENDENTES"
                            android:textColor="@color/white"
                            android:textSize="11sp"
                            android:textStyle="bold"
                            android:textAlignment="center"
                            android:lineSpacingExtra="1dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Espaço para informações do sistema -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Sistema PDV - Gutty"
                    android:textColor="@color/gray"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="Versão 1.4"
                    android:textColor="@color/gray"
                    android:textSize="10sp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>

package com.example.guttycielo.activities

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.guttycielo.adapters.CartAdapter
import com.example.guttycielo.databinding.ActivityCheckoutBinding
import com.example.guttycielo.models.CartItem
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.CartManager
import com.example.guttycielo.utils.ConnectionManager
import com.example.guttycielo.utils.SessionManager
import com.example.guttycielo.utils.CieloPrinter
import com.example.guttycielo.models.PendingOrder
import com.example.guttycielo.models.PendingOrderItem
import com.example.guttycielo.config.CieloLioConfig
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.util.Locale

// Imports da integração Cielo LIO via Deep Link
import com.example.guttycielo.cielo.CieloLioDeepLink
import android.content.Intent

class CheckoutActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCheckoutBinding
    private lateinit var cartAdapter: CartAdapter
    private var cartItems = listOf<CartItem>()
    private var recentCustomers = listOf<com.example.guttycielo.network.Customer>()

    // Integração Cielo LIO via Deep Link
    private lateinit var cieloLioDeepLink: CieloLioDeepLink

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCheckoutBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        loadCartItems()
        loadRecentCustomers()
        initializeCieloLio()
    }

    private fun setupViews() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        cartAdapter = CartAdapter(
            onQuantityChanged = { cartItem, newQuantity ->
                updateCartItemQuantity(cartItem, newQuantity)
            },
            onRemoveItem = { cartItem ->
                removeCartItem(cartItem)
            }
        )

        binding.rvCartItems.apply {
            layoutManager = LinearLayoutManager(this@CheckoutActivity)
            adapter = cartAdapter
        }

        setupButtons()
    }

    private fun setupAutoComplete() {
        // TextWatcher para nome do cliente
        binding.etCustomerName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                try {
                    val name = s.toString().trim()
                    if (name.isNotEmpty()) {
                        val customer = recentCustomers.find { it.nome.equals(name, ignoreCase = true) }
                        if (customer != null) {
                            // Remove o listener temporariamente para evitar loop
                            binding.etTable.removeTextChangedListener(tableTextWatcher)
                            binding.etTable.setText(customer.comanda.toString())
                            binding.etTable.addTextChangedListener(tableTextWatcher)
                        }
                    }
                } catch (e: Exception) {
                    // Ignora erros para evitar crash
                }
            }
        })

        // Cria o TextWatcher para mesa como variável para poder remover/adicionar
        setupTableTextWatcher()
    }

    private lateinit var tableTextWatcher: TextWatcher

    private fun setupTableTextWatcher() {
        tableTextWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                try {
                    val tableText = s.toString().trim()
                    if (tableText.isNotEmpty()) {
                        val table = tableText.toIntOrNull()
                        if (table != null && table > 0) {
                            val customer = recentCustomers.find { it.comanda == table }
                            if (customer != null) {
                                // Remove o listener temporariamente para evitar loop
                                binding.etCustomerName.removeTextChangedListener(customerTextWatcher)
                                binding.etCustomerName.setText(customer.nome)
                                binding.etCustomerName.addTextChangedListener(customerTextWatcher)
                            }
                        }
                    }
                } catch (e: Exception) {
                    // Ignora erros para evitar crash
                }
            }
        }

        binding.etTable.addTextChangedListener(tableTextWatcher)
    }

    private lateinit var customerTextWatcher: TextWatcher

    private fun setupCustomerTextWatcher() {
        customerTextWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                try {
                    val name = s.toString().trim()
                    if (name.isNotEmpty()) {
                        val customer = recentCustomers.find { it.nome.equals(name, ignoreCase = true) }
                        if (customer != null) {
                            binding.etTable.removeTextChangedListener(tableTextWatcher)
                            binding.etTable.setText(customer.comanda.toString())
                            binding.etTable.addTextChangedListener(tableTextWatcher)
                        }
                    }
                } catch (e: Exception) {
                    // Ignora erros para evitar crash
                }
            }
        }

        binding.etCustomerName.addTextChangedListener(customerTextWatcher)
    }

    private fun setupButtons() {
        binding.btnListCustomers.setOnClickListener {
            showCustomersList()
        }

        binding.btnListTables.setOnClickListener {
            showTablesList()
        }



        // Botão para finalizar pedido sem pagamento
        binding.btnFinalize.setOnClickListener {
            finalizeOrder()
        }

        // Botão para finalizar com pagamento
        binding.btnFinalizeWithPayment.setOnClickListener {
            finalizeOrderWithPayment()
        }

        // Botão para remover todos os itens
        binding.btnRemoveAllItems.setOnClickListener {
            removeAllItems()
        }
    }

    private fun showCustomersList() {
        if (recentCustomers.isEmpty()) {
            Toast.makeText(this, "Nenhum cliente com pedidos pendentes encontrado", Toast.LENGTH_SHORT).show()
            return
        }

        val customerNames = recentCustomers.map { it.nome }.distinct().toTypedArray()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Clientes com Pedidos Pendentes")
            .setItems(customerNames) { _, which ->
                val selectedCustomer = customerNames[which]
                binding.etCustomerName.setText(selectedCustomer)

                // Busca a comanda associada ao cliente
                val customer = recentCustomers.find { it.nome == selectedCustomer }
                if (customer != null) {
                    binding.etTable.setText(customer.comanda.toString())
                }

                Toast.makeText(this, "Cliente selecionado: $selectedCustomer", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("CANCELAR") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun showTablesList() {
        if (recentCustomers.isEmpty()) {
            Toast.makeText(this, "Nenhuma comanda com pedidos pendentes encontrada", Toast.LENGTH_SHORT).show()
            return
        }

        val tableNumbers = recentCustomers.map { it.comanda }.distinct().sorted().toTypedArray()
        val tableStrings = tableNumbers.map { "Mesa/Comanda $it" }.toTypedArray()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Comandas com Pedidos Pendentes")
            .setItems(tableStrings) { _, which ->
                val selectedTable = tableNumbers[which]
                binding.etTable.setText(selectedTable.toString())

                // Busca o cliente associado à comanda
                val customer = recentCustomers.find { it.comanda == selectedTable }
                if (customer != null) {
                    binding.etCustomerName.setText(customer.nome)
                }

                Toast.makeText(this, "Comanda selecionada: $selectedTable", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("CANCELAR") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun loadCartItems() {
        cartItems = CartManager.getCartItems(this)
        cartAdapter.submitList(cartItems)
        updateTotal()
    }

    private fun loadRecentCustomers() {
        lifecycleScope.launch {
            try {
                // Recarrega a conexão
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@CheckoutActivity)
                
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)
                    
                    recentCustomers = DatabaseManager.getRecentCustomers()
                    setupCustomerAutoComplete()
                }
            } catch (e: Exception) {
                Toast.makeText(this@CheckoutActivity, "Erro ao carregar clientes: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun setupCustomerAutoComplete() {
        val customerNames = recentCustomers.map { it.nome }.distinct()
        val customerAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, customerNames)
        (binding.etCustomerName as AutoCompleteTextView).setAdapter(customerAdapter)

        val tableNumbers = recentCustomers.map { it.comanda.toString() }.distinct()
        val tableAdapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, tableNumbers)
        (binding.etTable as AutoCompleteTextView).setAdapter(tableAdapter)

        // Configura os TextWatchers após carregar os dados
        setupAutoComplete()
    }

    private fun updateCartItemQuantity(cartItem: CartItem, newQuantity: Int) {
        CartManager.updateQuantity(this, cartItem.product.codigoGtin, newQuantity)
        loadCartItems() // Recarrega os itens
    }

    private fun removeCartItem(cartItem: CartItem) {
        // Confirma a remoção
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Remover Item")
            .setMessage("Deseja remover ${cartItem.product.descricao} do pedido?")
            .setPositiveButton("REMOVER") { _, _ ->
                CartManager.removeFromCart(this, cartItem.product.codigoGtin)
                loadCartItems() // Recarrega os itens
                Toast.makeText(this, "Item removido do pedido", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("CANCELAR") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun updateTotal() {
        val total = CartManager.getCartTotal(this)
        val currencyFormat = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
        binding.tvTotal.text = "Total: ${currencyFormat.format(total)}"
    }

    private fun finalizeOrder() {
        val customerName = binding.etCustomerName.text.toString().trim()
        val tableText = binding.etTable.text.toString().trim()
        val observation = binding.etObservation.text.toString().trim()

        // Validação
        if (customerName.isEmpty() && tableText.isEmpty()) {
            Toast.makeText(this, "Informe pelo menos o nome do cliente ou a comanda/mesa", Toast.LENGTH_LONG).show()
            return
        }

        if (cartItems.isEmpty()) {
            Toast.makeText(this, "Carrinho vazio", Toast.LENGTH_SHORT).show()
            return
        }

        // Desabilita o botão para evitar duplo clique
        binding.btnFinalize.isEnabled = false
        binding.btnFinalize.text = "FINALIZANDO..."

        lifecycleScope.launch {
            try {
                // Recarrega a conexão
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@CheckoutActivity)

                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    // Prepara os dados do pedido
                    val table = if (tableText.isNotEmpty()) {
                        tableText.toIntOrNull() ?: DatabaseManager.getNextTableNumber()
                    } else {
                        DatabaseManager.getNextTableNumber()
                    }
                    val finalCustomerName = customerName.ifEmpty { null }

                    // Pega o código do vendedor logado
                    val operador = SessionManager.getVendedorCodigo(this@CheckoutActivity)

                    // Salva o pedido
                    val success = DatabaseManager.saveOrder(cartItems, finalCustomerName, table, observation, operador)

                    if (success) {
                        // Cria objeto do pedido para possível impressão
                        val finalizedOrder = createOrderFromCart(finalCustomerName ?: "CLIENTE", table.toString())

                        // Limpa o carrinho
                        CartManager.clearCart(this@CheckoutActivity)

                        // Mostra opção de impressão
                        showPrintDialog(finalizedOrder)
                    } else {
                        Toast.makeText(this@CheckoutActivity, "Erro ao finalizar pedido. Tente novamente.", Toast.LENGTH_LONG).show()
                    }
                } else {
                    Toast.makeText(this@CheckoutActivity, "Conexão perdida. Verifique a conexão.", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Toast.makeText(this@CheckoutActivity, "Erro ao finalizar pedido: ${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                // Reabilita o botão
                binding.btnFinalize.isEnabled = true
                binding.btnFinalize.text = "FINALIZAR PEDIDO"
            }
        }
    }

    /**
     * Inicializa a integração Cielo LIO via Deep Link
     */
    private fun initializeCieloLio() {
        cieloLioDeepLink = CieloLioDeepLink(this)
        android.util.Log.d("CheckoutActivity", "✅ Integração Cielo LIO via Deep Link inicializada")
    }






    /**
     * Remove todos os itens do carrinho e volta para a MainActivity
     */
    private fun removeAllItems() {
        // Mostra dialog de confirmação
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Remover Todos os Itens")
            .setMessage("Tem certeza que deseja remover todos os itens do carrinho? Esta ação não pode ser desfeita.")
            .setPositiveButton("SIM, REMOVER") { _, _ ->
                // Remove todos os itens do carrinho
                CartManager.clearCart(this)

                // Mostra mensagem de confirmação
                Toast.makeText(this, "Todos os itens foram removidos", Toast.LENGTH_SHORT).show()

                // Volta para a MainActivity
                finish()
            }
            .setNegativeButton("CANCELAR") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun createOrderFromCart(customerName: String, tableNumber: String): PendingOrder {
        val orderItems: List<PendingOrderItem> = cartItems.map { cartItem ->
            PendingOrderItem(
                id = 0, // Não usado para impressão
                productCode = cartItem.product.codigoGtin,
                productName = cartItem.product.descricao,
                quantity = cartItem.quantidade,
                unitPrice = cartItem.product.precoVenda,
                totalPrice = cartItem.product.precoVenda * cartItem.quantidade,
                gtin = cartItem.product.codigoGtin
            )
        }

        return PendingOrder(
            customerName = customerName,
            tableNumber = tableNumber,
            totalItems = cartItems.sumOf { it.quantidade },
            totalAmount = cartItems.sumOf { (it.product.precoVenda * it.quantidade).toDouble() },
            items = orderItems
        )
    }

    private fun showPrintDialog(order: PendingOrder) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🖨️ Imprimir Pedido")
            .setMessage("Pedido finalizado com sucesso!\n\nDeseja imprimir o pedido agora?")
            .setPositiveButton("🖨️ IMPRIMIR") { _, _ ->
                printOrder(order)
                finish()
            }
            .setNegativeButton("❌ NÃO IMPRIMIR") { _, _ ->
                Toast.makeText(this@CheckoutActivity, "Pedido finalizado com sucesso!", Toast.LENGTH_LONG).show()
                finish()
            }
            .setCancelable(false)
            .show()
    }

    private fun finalizeOrderWithPayment() {
        val customerName = binding.etCustomerName.text.toString().trim()
        val tableText = binding.etTable.text.toString().trim()
        val observation = binding.etObservation.text.toString().trim()

        // Validação
        if (customerName.isEmpty() && tableText.isEmpty()) {
            Toast.makeText(this, "Informe pelo menos o nome do cliente ou a comanda/mesa", Toast.LENGTH_LONG).show()
            return
        }

        if (cartItems.isEmpty()) {
            Toast.makeText(this, "Carrinho vazio", Toast.LENGTH_SHORT).show()
            return
        }

        // Desabilita o botão para evitar duplo clique
        binding.btnFinalizeWithPayment.isEnabled = false
        binding.btnFinalizeWithPayment.text = "FINALIZANDO..."

        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@CheckoutActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    // Prepara os dados do pedido
                    val table = if (tableText.isNotEmpty()) {
                        tableText.toIntOrNull() ?: DatabaseManager.getNextTableNumber()
                    } else {
                        DatabaseManager.getNextTableNumber()
                    }
                    val finalCustomerName = customerName.ifEmpty { null }

                    // Pega o código do vendedor logado
                    val operador = SessionManager.getVendedorCodigo(this@CheckoutActivity)

                    // Salva o pedido
                    val success = DatabaseManager.saveOrder(cartItems, finalCustomerName, table, observation, operador)

                    if (success) {
                        // Cria objeto do pedido para impressão e pagamento
                        // Usa o nome real salvo no banco (ou vazio se não tiver)
                        val finalizedOrder = createOrderFromCart(finalCustomerName ?: "", table.toString())

                        // Limpa o carrinho
                        CartManager.clearCart(this@CheckoutActivity)

                        // Pergunta se quer imprimir
                        showPrintAndPaymentDialog(finalizedOrder)
                    } else {
                        Toast.makeText(this@CheckoutActivity, "Erro ao finalizar pedido. Tente novamente.", Toast.LENGTH_LONG).show()
                    }
                } else {
                    Toast.makeText(this@CheckoutActivity, "Conexão perdida. Verifique a conexão.", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Toast.makeText(this@CheckoutActivity, "Erro ao finalizar pedido: ${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                // Reabilita o botão
                binding.btnFinalizeWithPayment.isEnabled = true
                binding.btnFinalizeWithPayment.text = "FINALIZAR COM PAGAMENTO"
            }
        }
    }

    private fun showPrintAndPaymentDialog(order: PendingOrder) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("💳 Finalizar com Pagamento")
            .setMessage("Pedido finalizado com sucesso!\n\nDeseja imprimir o pedido antes de pagar?")
            .setPositiveButton("🖨️ IMPRIMIR E PAGAR") { _, _ ->
                // Salva dados para ir ao pagamento após impressão
                saveOrderForPaymentAfterPrint(order)
                printOrder(order)
            }
            .setNegativeButton("💳 PAGAR SEM IMPRIMIR") { _, _ ->
                // Vai direto para pagamento
                openOrderPayment(order)
            }
            .setCancelable(false)
            .show()
    }

    private fun saveOrderForPaymentAfterPrint(order: PendingOrder) {
        // Salva dados do pedido para ir ao pagamento após impressão
        val sharedPrefs = getSharedPreferences("payment_after_print", MODE_PRIVATE)
        sharedPrefs.edit()
            .putBoolean("should_go_to_payment", true)
            .putString("customer_name", order.customerName)
            .putString("table_number", order.tableNumber)
            .apply()
    }

    private fun openOrderPayment(order: PendingOrder) {
        val intent = Intent(this, OrderPaymentActivity::class.java).apply {
            putExtra("customer_name", order.customerName)
            putExtra("table_number", order.tableNumber)
            putExtra("total_amount", order.totalAmount)
            putExtra("total_items", order.totalItems)
            putExtra("is_view_only", false) // Modo pagamento
        }
        startActivity(intent)
        finish()
    }

    private fun printOrder(order: PendingOrder) {
        try {
            val printer = CieloPrinter(this)
            printer.printOrder(order)
            Toast.makeText(this, "🖨️ Enviando para impressão...", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(this, "❌ Erro ao imprimir: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

}

<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="88dp"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    android:clickable="true"
    android:focusable="true"
    android:backgroundTint="@color/background_card"
    app:cardCornerRadius="16dp"
    app:cardElevation="6dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/stroke_light"
    app:rippleColor="@color/primary_blue_light">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="20dp">

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:src="@drawable/ic_category_prod"
            android:backgroundTint="@color/primary_blue_light"
            android:padding="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/primary_blue" />

        <TextView
            android:id="@+id/tvName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="16dp"
            android:textColor="@color/text_primary"
            android:textSize="20sp"
            android:textStyle="bold"
            android:letterSpacing="0.02"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivArrow"
            app:layout_constraintStart_toEndOf="@+id/ivIcon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="BEBIDAS" />

        <ImageView
            android:id="@+id/ivArrow"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/ic_arrow_forward"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/accent_yellow_dark" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>

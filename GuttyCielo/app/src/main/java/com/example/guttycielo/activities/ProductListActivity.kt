package com.example.guttycielo.activities

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.guttycielo.adapters.ProductAdapter
import com.example.guttycielo.databinding.ActivityProductListBinding
import com.example.guttycielo.models.Product
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.ConnectionManager
import kotlinx.coroutines.launch

class ProductListActivity : AppCompatActivity() {

    private lateinit var binding: ActivityProductListBinding
    private lateinit var productAdapter: ProductAdapter
    private var allProducts = listOf<Product>()
    private var grupo: String = ""
    private var categoria: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        categoria = intent.getStringExtra("categoria") ?: "" // categoria principal
        grupo = intent.getStringExtra("grupo") ?: ""         // grupo = subcategoria

        setupViews()
        loadProducts()
    }

    private fun setupViews() {
        // Atualiza o título (categoria = principal, grupo = subcategoria)
        binding.toolbar.title = "$categoria - $grupo"
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        // Configurar RecyclerView
        productAdapter = ProductAdapter { product ->
            onProductClick(product)
        }
        
        binding.rvProducts.apply {
            layoutManager = LinearLayoutManager(this@ProductListActivity)
            adapter = productAdapter
        }

        // Configurar busca
        binding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                filterProducts(s.toString())
            }
        })

        // Configurar botão de refresh
        binding.fabRefresh.setOnClickListener {
            loadProducts()
        }
    }

    private fun loadProducts() {
        showLoading(true)

        lifecycleScope.launch {
            try {
                if (!DatabaseManager.isConnected()) {
                    Toast.makeText(this@ProductListActivity, "Conexão com banco perdida. Retornando...", Toast.LENGTH_LONG).show()
                    finish()
                    return@launch
                }

                val products = DatabaseManager.getProductsByCategory(categoria)
                allProducts = products
                productAdapter.submitList(products)

                if (products.isEmpty()) {
                    showEmptyState(true)
                } else {
                    showEmptyState(false)
                }

            } catch (e: Exception) {
                Toast.makeText(this@ProductListActivity, "Erro ao carregar produtos: ${e.message}", Toast.LENGTH_LONG).show()
                showEmptyState(true)
            } finally {
                showLoading(false)
            }
        }
    }

    private fun filterProducts(query: String) {
        val filteredProducts = if (query.isEmpty()) {
            allProducts
        } else {
            allProducts.filter { product ->
                product.descricao.contains(query, ignoreCase = true) ||
                product.descricaoDetalhada.contains(query, ignoreCase = true) ||
                product.categoria.contains(query, ignoreCase = true)
            }
        }

        productAdapter.submitList(filteredProducts)
        showEmptyState(filteredProducts.isEmpty() && query.isNotEmpty())
    }

    private fun onProductClick(product: Product) {
        Toast.makeText(this, "Produto selecionado: ${product.descricao}", Toast.LENGTH_SHORT).show()
        // Aqui você pode implementar a navegação para detalhes do produto
    }

    private fun showLoading(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
        binding.rvProducts.visibility = if (show) View.GONE else View.VISIBLE
    }

    private fun showEmptyState(show: Boolean) {
        binding.tvEmptyState.visibility = if (show) View.VISIBLE else View.GONE
        binding.rvProducts.visibility = if (show) View.GONE else View.VISIBLE
    }

    override fun onResume() {
        super.onResume()

        // Recarrega a conexão quando a activity volta ao foco
        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@ProductListActivity)

                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)
                } else {
                    Toast.makeText(this@ProductListActivity, "Conexão perdida. Retornando...", Toast.LENGTH_LONG).show()
                    finish()
                }
            } catch (e: Exception) {
                Toast.makeText(this@ProductListActivity, "Erro ao recarregar conexão: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Não fecha mais a conexão aqui, mantém para outras telas
    }
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="@color/white">

    <!-- Ícone do resultado -->
    <ImageView
        android:id="@+id/ivResultIcon"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginBottom="24dp"
        android:src="@drawable/circle_info_solid"
        app:tint="@color/light_blue" />

    <!-- T<PERSON>tulo do resultado -->
    <TextView
        android:id="@+id/tvResultTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Resultado do Pagamento"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Mensagem do resultado -->
    <TextView
        android:id="@+id/tvResultMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Processando resultado..."
        android:textSize="16sp"
        android:textColor="@color/gray"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Detalhes da transação -->
    <TextView
        android:id="@+id/tvTransactionDetails"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="ID da Transação: 123456"
        android:textSize="14sp"
        android:textColor="@color/gray"
        android:gravity="center"
        android:layout_marginBottom="8dp"
        android:visibility="gone" />

    <!-- Detalhes do valor -->
    <TextView
        android:id="@+id/tvAmountDetails"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Valor: R$ 20,00"
        android:textSize="14sp"
        android:textColor="@color/gray"
        android:gravity="center"
        android:layout_marginBottom="8dp"
        android:visibility="gone" />

    <!-- Forma de pagamento -->
    <TextView
        android:id="@+id/tvPaymentMethod"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="FORMA DE PAGAMENTO: DINHEIRO"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/light_blue"
        android:gravity="center"
        android:layout_marginBottom="32dp"
        android:visibility="gone" />

    <!-- Botão de ação -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnAction"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginTop="24dp"
        android:text="TENTAR NOVAMENTE"
        android:textSize="16sp"
        android:backgroundTint="@color/light_blue"
        android:textColor="@color/white"
        app:cornerRadius="8dp" />

</LinearLayout>

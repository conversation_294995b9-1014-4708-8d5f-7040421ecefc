package com.example.guttycielo.config

/**
 * Configurações para integração com Cielo LIO via SDK Local
 * 
 * Para obter suas credenciais:
 * 1. Acesse o Portal de Desenvolvedores da Cielo: https://desenvolvedores.cielo.com.br
 * 2. <PERSON>a<PERSON> login ou crie uma conta
 * 3. Vá em "Client ID Cadastrados" para obter seu CLIENT_ID e ACCESS_TOKEN
 * 
 * IMPORTANTE: 
 * - Substitua os valores abaixo pelas suas credenciais reais
 * - Nunca commite credenciais reais no controle de versão
 * - Use variáveis de ambiente ou arquivos de configuração seguros em produção
 */
object CieloLioConfig {
    
    /**
     * Client ID obtido no Portal de Desenvolvedores da Cielo
     * Identificação de acesso para sua aplicação
     */
    const val CLIENT_ID = "JGJGR0Svcb8sCAEwGQXuKvqlhYnxgtORB8TCkLmIVlOI4vVXnc"
    
    /**
     * Access Token obtido no Portal de Desenvolvedores da Cielo
     * Token de acesso que armazena as regras de acesso permitidas ao Client ID
     */
    const val ACCESS_TOKEN = "b4ceXuRR376G0XAnjksbGAvk9Y6W01Un45p4qhh5vnm8bHpMeV"
    
    /**
     * Verifica se as credenciais foram configuradas
     */
    fun isConfigured(): Boolean {
        return CLIENT_ID != "SEU_CLIENT_ID_AQUI" && 
               ACCESS_TOKEN != "SEU_ACCESS_TOKEN_AQUI" &&
               CLIENT_ID.isNotEmpty() && 
               ACCESS_TOKEN.isNotEmpty()
    }
    
    /**
     * Retorna uma mensagem de erro se as credenciais não estiverem configuradas
     */
    fun getConfigurationError(): String {
        return """
            Credenciais da Cielo LIO não configuradas!

            Para configurar:
            1. Acesse: https://desenvolvedores.cielo.com.br
            2. Obtenha seu CLIENT_ID e ACCESS_TOKEN
            3. Atualize os valores em CieloLioConfig.kt

            Valores atuais:
            CLIENT_ID: $CLIENT_ID
            ACCESS_TOKEN: ${ACCESS_TOKEN.take(10)}...
        """.trimIndent()
    }

    /**
     * FUNÇÃO TEMPORÁRIA DE DEBUG - Remove após descobrir as informações corretas
     *
     * Esta função lista todos os apps instalados relacionados à Cielo
     * e suas informações para descobrir como chamar corretamente o app da LIO
     */
    fun debugCieloApps(context: android.content.Context): String {
        val packageManager = context.packageManager
        val installedApps = packageManager.getInstalledApplications(android.content.pm.PackageManager.GET_META_DATA)

        val cieloApps = mutableListOf<String>()

        // Procura por apps que contenham "cielo", "lio", "payment" ou "launcher" no nome do pacote
        installedApps.forEach { appInfo ->
            val packageName = appInfo.packageName.lowercase()
            if (packageName.contains("cielo") ||
                packageName.contains("lio") ||
                packageName.contains("payment") ||
                packageName.contains("launcher") ||
                packageName.contains("emulator")) {
                try {
                    val appName = packageManager.getApplicationLabel(appInfo).toString()
                    val packageName = appInfo.packageName
                    val versionInfo = packageManager.getPackageInfo(packageName, 0)
                    val versionName = versionInfo.versionName ?: "Desconhecida"
                    val versionCode = versionInfo.versionCode

                    // Tenta encontrar a activity principal (launcher)
                    val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
                    val mainActivity = launchIntent?.component?.className ?: "Não encontrada"

                    cieloApps.add("""
                        📱 APP ENCONTRADO:
                        Nome: $appName
                        Pacote: $packageName
                        Versão: $versionName ($versionCode)
                        Activity Principal: $mainActivity

                        🔍 TESTANDO INTENTS:
                        ${testCieloIntents(context, packageName)}

                        ═══════════════════════════════════════
                    """.trimIndent())

                } catch (e: Exception) {
                    cieloApps.add("❌ Erro ao obter info do pacote: ${appInfo.packageName} - ${e.message}")
                }
            }
        }

        return if (cieloApps.isNotEmpty()) {
            """
            🔍 APPS DA CIELO ENCONTRADOS:

            ${cieloApps.joinToString("\n")}

            💡 INSTRUÇÕES:
            1. Copie essas informações
            2. Me passe os dados do app que você quer usar
            3. Vou atualizar o código com as informações corretas

            """.trimIndent()
        } else {
            """
            ❌ NENHUM APP DA CIELO ENCONTRADO!

            Verifique se você tem algum app da Cielo instalado:
            - Cielo LIO
            - LIO EMULATOR
            - Cielo Gestão
            - Outros apps da Cielo

            """.trimIndent()
        }
    }

    /**
     * Testa diferentes intents para ver quais funcionam com o app da Cielo
     */
    private fun testCieloIntents(context: android.content.Context, packageName: String): String {
        val packageManager = context.packageManager
        val results = mutableListOf<String>()

        // Lista de actions para testar
        val actionsToTest = listOf(
            "cielo.intent.action.PAYMENT",
            "cielo.intent.action.PAYMENT_V2",
            "cielo.launcher.PAYMENT",
            "android.intent.action.MAIN"
        )

        // Lista de activities para testar
        val activitiesToTest = listOf(
            "MainActivity",
            "PaymentActivity",
            "LauncherActivity",
            "cielo.launcher.MainActivity",
            "cielo.lio.emulator.MainActivity"
        )

        actionsToTest.forEach { action ->
            try {
                val intent = android.content.Intent(action)
                intent.setPackage(packageName)
                val resolveInfo = packageManager.resolveActivity(intent, 0)

                if (resolveInfo != null) {
                    results.add("✅ Action '$action' → FUNCIONA")
                } else {
                    results.add("❌ Action '$action' → Não funciona")
                }
            } catch (e: Exception) {
                results.add("❌ Action '$action' → Erro: ${e.message}")
            }
        }

        // Testa activities específicas
        activitiesToTest.forEach { activityName ->
            try {
                val fullActivityName = if (activityName.contains(".")) {
                    activityName
                } else {
                    "$packageName.$activityName"
                }

                val intent = android.content.Intent()
                intent.setClassName(packageName, fullActivityName)
                val resolveInfo = packageManager.resolveActivity(intent, 0)

                if (resolveInfo != null) {
                    results.add("✅ Activity '$fullActivityName' → EXISTE")
                } else {
                    results.add("❌ Activity '$fullActivityName' → Não existe")
                }
            } catch (e: Exception) {
                results.add("❌ Activity '$activityName' → Erro: ${e.message}")
            }
        }

        return results.joinToString("\n        ")
    }
}

package com.example.guttycielo.network

import android.util.Log
import com.example.guttycielo.models.DatabaseConnection
import com.example.guttycielo.models.Product
import com.example.guttycielo.models.PaymentSplit
import com.example.guttycielo.models.PaymentSplitItem
import com.example.guttycielo.models.SplitStatus
import com.example.guttycielo.models.PaymentStatus
import com.example.guttycielo.models.PaymentMethod
import com.example.guttycielo.utils.LogHelper
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object NetworkManager {

    private const val TAG = "NetworkManager"
    private var BASE_URL = "http://192.168.1.100/guttycielo/" // URL padrão - ALTERE AQUI

    private val loggingInterceptor = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }

    private val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(loggingInterceptor)
        .connectTimeout(60, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()

    private var retrofit = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .build()

    var apiService: ApiService = retrofit.create(ApiService::class.java)
        private set

    fun updateBaseUrl(newBaseUrl: String) {
        var url = newBaseUrl
        if (!url.endsWith("/")) {
            url += "/"
        }

        BASE_URL = url
        retrofit = Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        apiService = retrofit.create(ApiService::class.java)
        LogHelper.d("Base URL atualizada para: $BASE_URL")
    }
}

object DatabaseManager {

    private const val TAG = "DatabaseManager"
    private var currentDbConnection: DatabaseConnection? = null

    suspend fun testConnection(dbConnection: DatabaseConnection): Pair<Boolean, String> {
        return try {
            LogHelper.d("=== INICIANDO TESTE DE CONEXÃO VIA HTTP ===")
            LogHelper.d("Host: ${dbConnection.host}")
            LogHelper.d("Port: ${dbConnection.port}")
            LogHelper.d("Database: ${dbConnection.database}")
            LogHelper.d("Username: ${dbConnection.username}")
            LogHelper.d("Servidor API: ${NetworkManager.apiService}")

            LogHelper.d("Enviando requisição de teste...")
            val response = NetworkManager.apiService.testConnection(
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            LogHelper.d("Resposta recebida. Código: ${response.code()}")

            if (response.isSuccessful) {
                val result = response.body()
                LogHelper.d("Body da resposta: $result")

                if (result?.success == true) {
                    currentDbConnection = dbConnection
                    LogHelper.d("✅ CONEXÃO ESTABELECIDA COM SUCESSO!")
                    Pair(true, result.message ?: "Conexão estabelecida com sucesso!")
                } else {
                    val error = result?.error ?: "Erro desconhecido"
                    LogHelper.e("❌ Falha na conexão: $error")
                    Pair(false, error)
                }
            } else {
                val error = "Erro HTTP: ${response.code()} - ${response.message()}"
                LogHelper.e(error)
                Pair(false, error)
            }
        } catch (e: Exception) {
            val error = "Erro de comunicação: ${e.message}"
            LogHelper.e(error, e)
            Pair(false, error)
        }
    }



    fun closeConnection() {
        currentDbConnection = null
        LogHelper.d("Conexão fechada")
    }

    fun isConnected(): Boolean {
        val connected = currentDbConnection != null
        LogHelper.d("Verificando conexão: $connected")
        return connected
    }

    fun setCurrentConnection(dbConnection: DatabaseConnection) {
        currentDbConnection = dbConnection
        LogHelper.d("Conexão configurada: ${dbConnection.host}")
    }

    suspend fun getCategories(): List<String> {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO CATEGORIAS ===")
            LogHelper.d("Conexão atual: ${dbConnection.host}")

            val response = NetworkManager.apiService.getCategories(
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            LogHelper.d("Resposta recebida: ${response.code()}")

            if (response.isSuccessful) {
                val result = response.body()
                LogHelper.d("Body da resposta: $result")

                if (result?.success == true) {
                    val categories = result.data ?: emptyList()
                    LogHelper.d("✅ Carregadas ${categories.size} categorias")
                    categories
                } else {
                    val error = result?.error ?: "Erro ao buscar categorias"
                    LogHelper.e("❌ $error")
                    emptyList()
                }
            } else {
                LogHelper.e("Erro HTTP ao buscar categorias: ${response.code()}")
                emptyList()
            }
        } catch (e: Exception) {
            LogHelper.e("Erro ao buscar categorias: ${e.message}", e)
            emptyList()
        }
    }

    suspend fun getProductsByCategory(categoria: String): List<Product> {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO PRODUTOS POR CATEGORIA ===")
            LogHelper.d("Categoria: $categoria")

            val response = NetworkManager.apiService.getProductsByCategory(
                categoria = categoria,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    val products = result.data ?: emptyList()
                    LogHelper.d("✅ Carregados ${products.size} produtos")
                    // Os produtos já vêm no formato correto do PHP
                    products
                } else {
                    val error = result?.error ?: "Erro ao buscar produtos"
                    LogHelper.e("❌ $error")
                    emptyList()
                }
            } else {
                LogHelper.e("Erro HTTP ao buscar produtos: ${response.code()}")
                emptyList()
            }
        } catch (e: Exception) {
            LogHelper.e("Erro ao buscar produtos: ${e.message}", e)
            emptyList()
        }
    }

    suspend fun getRecentCustomers(): List<com.example.guttycielo.network.Customer> {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO CLIENTES COM PEDIDOS NÃO IMPRESSOS ===")

            val response = NetworkManager.apiService.getRecentCustomers(
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    val customers = result.data ?: emptyList()
                    LogHelper.d("✅ Carregados ${customers.size} clientes com pedidos não impressos")
                    customers
                } else {
                    val error = result?.error ?: "Erro ao buscar clientes"
                    LogHelper.e("❌ $error")
                    emptyList()
                }
            } else {
                LogHelper.e("Erro HTTP ao buscar clientes: ${response.code()}")
                emptyList()
            }
        } catch (e: Exception) {
            LogHelper.e("Erro ao buscar clientes: ${e.message}", e)
            emptyList()
        }
    }

    suspend fun saveOrder(cartItems: List<com.example.guttycielo.models.CartItem>, customerName: String?, table: Int, observation: String, operador: Int): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== SALVANDO PEDIDO ===")
            LogHelper.d("Cliente: $customerName, Mesa: $table, Operador: $operador, Itens: ${cartItems.size}")

            // Prepara os dados do pedido
            val orderData = mapOf(
                "items" to cartItems.map { cartItem ->
                    mapOf(
                        "product" to mapOf(
                            "codigoGtin" to cartItem.product.codigoGtin,
                            "descricao" to cartItem.product.descricao,
                            "precoVenda" to cartItem.product.precoVenda
                        ),
                        "quantidade" to cartItem.quantidade
                    )
                },
                "customerName" to customerName,
                "table" to table,
                "observation" to observation
            )

            val orderJson = com.google.gson.Gson().toJson(orderData)

            val response = NetworkManager.apiService.saveOrder(
                orderData = orderJson,
                operador = operador,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    LogHelper.d("✅ Pedido salvo com sucesso")
                    true
                } else {
                    val error = result?.error ?: "Erro ao salvar pedido"
                    LogHelper.e("❌ $error")
                    false
                }
            } else {
                LogHelper.e("Erro HTTP ao salvar pedido: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("Erro ao salvar pedido: ${e.message}", e)
            false
        }
    }

    suspend fun getAllOrders(): List<com.example.guttycielo.network.OrderItem> {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO PEDIDOS NÃO IMPRESSOS ===")

            val response = NetworkManager.apiService.getAllOrders(
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    val orders = result.data ?: emptyList()
                    LogHelper.d("✅ Carregados ${orders.size} pedidos não impressos")
                    orders
                } else {
                    val error = result?.error ?: "Erro ao buscar pedidos"
                    LogHelper.e("❌ $error")
                    emptyList()
                }
            } else {
                LogHelper.e("Erro HTTP ao buscar pedidos: ${response.code()}")
                emptyList()
            }
        } catch (e: Exception) {
            LogHelper.e("Erro ao buscar pedidos: ${e.message}", e)
            emptyList()
        }
    }

    suspend fun getNextTableNumber(): Int {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO PRÓXIMO NÚMERO DE MESA ===")

            val response = NetworkManager.apiService.getNextTableNumber(
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    val nextTable = result.data ?: 1
                    LogHelper.d("✅ Próximo número de mesa: $nextTable")
                    nextTable
                } else {
                    val error = result?.error ?: "Erro ao buscar próximo número"
                    LogHelper.e("❌ $error")
                    1 // Fallback para 1
                }
            } else {
                LogHelper.e("Erro HTTP ao buscar próximo número: ${response.code()}")
                1 // Fallback para 1
            }
        } catch (e: Exception) {
            LogHelper.e("Erro ao buscar próximo número: ${e.message}", e)
            1 // Fallback para 1
        }
    }

    suspend fun markOrderAsPaid(table: Int, paymentType: String = "DEBITO"): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== MARCANDO PEDIDO COMO PAGO ===")
            LogHelper.d("Mesa/Comanda: $table")
            LogHelper.d("Tipo de Pagamento: $paymentType")

            val response = NetworkManager.apiService.markOrderAsPaid(
                table = table,
                paymentType = paymentType,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    LogHelper.d("✅ Pedido marcado como pago: ${result.message}")
                    true
                } else {
                    val error = result?.error ?: "Erro ao marcar pedido como pago"
                    LogHelper.e("❌ $error")
                    false
                }
            } else {
                LogHelper.e("Erro HTTP ao marcar pedido como pago: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("Erro ao marcar pedido como pago: ${e.message}", e)
            false
        }
    }

    suspend fun getPendingOrders(searchQuery: String = ""): List<com.example.guttycielo.models.PendingOrder> {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO PEDIDOS EM ABERTO ===")
            LogHelper.d("Query de busca: $searchQuery")

            val response = NetworkManager.apiService.getPendingOrders(
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password,
                searchQuery = searchQuery
            )

            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true && apiResponse.data != null) {
                    LogHelper.d("✅ ${apiResponse.data.size} pedidos encontrados")

                    // Converte a resposta da API para o modelo local
                    apiResponse.data.map { orderResponse ->
                        com.example.guttycielo.models.PendingOrder(
                            customerName = orderResponse.customer_name,
                            tableNumber = orderResponse.table_number,
                            totalItems = orderResponse.total_items,
                            totalAmount = orderResponse.total_amount,
                            items = orderResponse.items.map { itemResponse ->
                                com.example.guttycielo.models.PendingOrderItem(
                                    id = itemResponse.id,
                                    productCode = itemResponse.product_code,
                                    productName = itemResponse.product_name,
                                    quantity = itemResponse.quantity,
                                    unitPrice = itemResponse.unit_price,
                                    totalPrice = itemResponse.total_price,
                                    gtin = itemResponse.gtin
                                )
                            }
                        )
                    }
                } else {
                    LogHelper.e("❌ Erro na API: ${apiResponse?.error}")
                    emptyList()
                }
            } else {
                LogHelper.e("❌ Erro HTTP: ${response.code()} - ${response.message()}")
                emptyList()
            }

        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao buscar pedidos: ${e.message}")
            emptyList()
        }
    }

    suspend fun updateOrderItems(tableNumber: String, customerName: String, items: List<com.example.guttycielo.models.PendingOrderItem>): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== ATUALIZANDO ITENS DO PEDIDO ===")
            LogHelper.d("Mesa: $tableNumber")
            LogHelper.d("Cliente: $customerName")
            LogHelper.d("Itens: ${items.size}")

            // Converte itens para JSON
            val itemsJson = org.json.JSONArray()
            items.forEach { item ->
                val itemJson = org.json.JSONObject()
                itemJson.put("id", item.id)
                itemJson.put("product_code", item.productCode)
                itemJson.put("product_name", item.productName)
                itemJson.put("quantity", item.quantity)
                itemJson.put("unit_price", item.unitPrice)
                itemJson.put("total_price", item.totalPrice)
                itemJson.put("gtin", item.gtin)
                itemsJson.put(itemJson)
            }

            val response = NetworkManager.apiService.updateOrderItems(
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password,
                tableNumber = tableNumber,
                customerName = customerName,
                itemsJson = itemsJson.toString()
            )

            if (response.isSuccessful) {
                val apiResponse = response.body()
                if (apiResponse?.success == true) {
                    LogHelper.d("✅ Itens do pedido atualizados com sucesso")
                    true
                } else {
                    LogHelper.e("❌ Erro na API: ${apiResponse?.error}")
                    false
                }
            } else {
                LogHelper.e("❌ Erro HTTP: ${response.code()} - ${response.message()}")
                false
            }

        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao atualizar itens do pedido: ${e.message}")
            false
        }
    }

    suspend fun markOrderAsPaid(tableNumber: String, customerName: String, transactionId: String, paymentType: String = "DEBITO"): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== MARCANDO PEDIDO COMO PAGO ===")
            LogHelper.d("Mesa/Comanda: $tableNumber")
            LogHelper.d("Cliente: $customerName")
            LogHelper.d("Transaction ID: $transactionId")
            LogHelper.d("Tipo de Pagamento: $paymentType")

            val response = NetworkManager.apiService.markOrderAsPaidByDetails(
                tableNumber = tableNumber,
                customerName = customerName,
                transactionId = transactionId,
                paymentType = paymentType,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    LogHelper.d("✅ Pedido marcado como pago: ${result.message}")
                    true
                } else {
                    val error = result?.error ?: "Erro ao marcar pedido como pago"
                    LogHelper.e("❌ $error")
                    false
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao marcar pedido como pago: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao marcar pedido como pago: ${e.message}")
            false
        }
    }

    suspend fun getPendingOrderItems(tableNumber: String, customerName: String): List<com.example.guttycielo.models.PendingOrderItem> {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO ITENS DO PEDIDO ===")
            LogHelper.d("Mesa: $tableNumber, Cliente: $customerName")

            val response = NetworkManager.apiService.getPendingOrders(
                searchQuery = if (customerName.isNotEmpty()) customerName else tableNumber,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    val orders = result.data ?: emptyList()

                    // Encontra o pedido específico e retorna seus itens
                    val targetOrder = orders.find { order ->
                        order.table_number == tableNumber &&
                        (customerName.isEmpty() || order.customer_name.equals(customerName, ignoreCase = true))
                    }

                    val items = targetOrder?.items ?: emptyList()
                    LogHelper.d("✅ Encontrados ${items.size} itens para o pedido")

                    // Converte PendingOrderItemResponse para PendingOrderItem
                    items.map { itemResponse ->
                        com.example.guttycielo.models.PendingOrderItem(
                            id = itemResponse.id,
                            productCode = itemResponse.product_code,
                            productName = itemResponse.product_name,
                            quantity = itemResponse.quantity,
                            unitPrice = itemResponse.unit_price,
                            totalPrice = itemResponse.total_price,
                            gtin = itemResponse.gtin
                        )
                    }
                } else {
                    val error = result?.error ?: "Erro ao buscar itens do pedido"
                    LogHelper.e("❌ $error")
                    emptyList()
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao buscar itens: ${response.code()}")
                emptyList()
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao buscar itens do pedido: ${e.message}")
            emptyList()
        }
    }

    suspend fun mergeComandas(comandaOrigem: String, comandaDestino: String): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== MERGE DE COMANDAS ===")
            LogHelper.d("Origem: $comandaOrigem")
            LogHelper.d("Destino: $comandaDestino")

            val response = NetworkManager.apiService.mergeComandas(
                comandaOrigem = comandaOrigem,
                comandaDestino = comandaDestino,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    LogHelper.d("✅ Comandas unidas com sucesso: ${result.message}")
                    true
                } else {
                    val error = result?.error ?: "Erro ao unir comandas"
                    LogHelper.e("❌ $error")
                    false
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao unir comandas: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao unir comandas: ${e.message}")
            false
        }
    }

    suspend fun getMergesRecentes(): List<com.example.guttycielo.models.MergeLog> {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO MERGES RECENTES ===")

            val response = NetworkManager.apiService.getMergesRecentes(
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    val merges = result.data ?: emptyList()
                    LogHelper.d("✅ Encontrados ${merges.size} merges recentes")
                    merges
                } else {
                    val error = result?.error ?: "Erro ao buscar merges"
                    LogHelper.e("❌ $error")
                    emptyList()
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao buscar merges: ${response.code()}")
                emptyList()
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao buscar merges recentes: ${e.message}")
            emptyList()
        }
    }

    suspend fun desfazerMerge(mergeId: Int): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== DESFAZENDO MERGE ===")
            LogHelper.d("Merge ID: $mergeId")

            val response = NetworkManager.apiService.desfazerMerge(
                mergeId = mergeId,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    LogHelper.d("✅ Merge desfeito com sucesso: ${result.message}")
                    true
                } else {
                    val error = result?.error ?: "Erro ao desfazer merge"
                    LogHelper.e("❌ $error")
                    false
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao desfazer merge: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao desfazer merge: ${e.message}")
            false
        }
    }

    suspend fun getComandasAbertas(): List<com.example.guttycielo.models.ComandaAberta> {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO COMANDAS ABERTAS ===")

            val response = NetworkManager.apiService.getComandasAbertas(
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()

                if (result?.success == true) {
                    val comandas = result.data ?: emptyList()
                    LogHelper.d("✅ Encontradas ${comandas.size} comandas abertas")
                    comandas
                } else {
                    val error = result?.error ?: "Erro ao buscar comandas abertas"
                    LogHelper.e("❌ $error")
                    emptyList()
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao buscar comandas abertas: ${response.code()}")
                emptyList()
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao buscar comandas abertas: ${e.message}")
            emptyList()
        }
    }

    suspend fun getVendedor(codigo: String): com.example.guttycielo.models.Vendedor? {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO VENDEDOR ===")
            LogHelper.d("Código: $codigo")

            val response = NetworkManager.apiService.getVendedor(
                codigo = codigo,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val vendedorResponse = response.body()
                if (vendedorResponse?.success == true) {
                    LogHelper.d("✅ Vendedor encontrado: ${vendedorResponse.vendedor?.nome}")
                    vendedorResponse.vendedor
                } else {
                    val errorMessage = vendedorResponse?.error ?: "Erro desconhecido"
                    LogHelper.e("❌ Erro do servidor: $errorMessage")
                    // Lança exceção com a mensagem específica do servidor
                    throw Exception(errorMessage)
                }
            } else {
                LogHelper.e("❌ Erro na requisição: ${response.code()}")
                throw Exception("Erro de conexão com o servidor")
            }

        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao buscar vendedor: ${e.message}")
            // Re-lança a exceção para que a Activity possa capturar a mensagem
            throw e
        }
    }

    suspend fun saveSplitPayment(split: PaymentSplit): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== SALVANDO DIVISÃO DE PAGAMENTO ===")
            LogHelper.d("Comanda ID: ${split.comandaId}")
            LogHelper.d("Total Parcelas: ${split.totalParcelas}")
            LogHelper.d("Valor Total: ${split.valorTotal}")

            val response = NetworkManager.apiService.saveSplitPayment(
                comandaId = split.comandaId,
                totalParcelas = split.totalParcelas,
                parcelasPagas = split.parcelasPageas,
                valorTotal = split.valorTotal,
                valorPago = split.valorPago,
                status = split.status.ordinal,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()
                if (result?.success == true) {
                    LogHelper.d("✅ Divisão salva: ${result.message}")
                    true
                } else {
                    LogHelper.e("❌ Erro ao salvar divisão: ${result?.error}")
                    false
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao salvar divisão: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao salvar divisão: ${e.message}")
            false
        }
    }

    suspend fun saveSplitPaymentItem(comandaId: Int, parcela: PaymentSplitItem): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== SALVANDO PARCELA DE PAGAMENTO ===")
            LogHelper.d("Comanda ID: $comandaId")
            LogHelper.d("Parcela: ${parcela.parcelaNumero}")
            LogHelper.d("Valor: ${parcela.valor}")
            LogHelper.d("Status: ${parcela.status}")

            val response = NetworkManager.apiService.saveSplitPaymentItem(
                comandaId = comandaId,
                parcelaNumero = parcela.parcelaNumero,
                valor = parcela.valor,
                formaPagamento = parcela.formaPagamento?.statusCode ?: 6,
                status = parcela.status.ordinal,
                dataPagamento = parcela.dataPagamento ?: "",
                cieloResponse = parcela.cieloResponse ?: "",
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()
                if (result?.success == true) {
                    LogHelper.d("✅ Parcela salva: ${result.message}")
                    true
                } else {
                    LogHelper.e("❌ Erro ao salvar parcela: ${result?.error}")
                    false
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao salvar parcela: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao salvar parcela: ${e.message}")
            false
        }
    }

    suspend fun markOrderAsPaidWithStatus(tableNumber: String, customerName: String, transactionId: String, status: Int): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== MARCANDO PEDIDO COMO PAGO COM STATUS ===")
            LogHelper.d("Mesa/Comanda: $tableNumber")
            LogHelper.d("Cliente: $customerName")
            LogHelper.d("Status: $status")

            val response = NetworkManager.apiService.markOrderAsPaidWithStatus(
                tableNumber = tableNumber,
                customerName = customerName,
                transactionId = transactionId,
                status = status,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()
                if (result?.success == true) {
                    LogHelper.d("✅ Pedido marcado como pago: ${result.message}")
                    true
                } else {
                    LogHelper.e("❌ Erro ao marcar pedido: ${result?.error}")
                    false
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao marcar pedido: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao marcar pedido: ${e.message}")
            false
        }
    }

    suspend fun createSplitTables(): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== CRIANDO TABELAS DE DIVISÃO ===")

            val response = NetworkManager.apiService.createSplitTables(
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()
                if (result?.success == true) {
                    LogHelper.d("✅ Tabelas criadas: ${result.message}")
                    true
                } else {
                    LogHelper.e("❌ Erro ao criar tabelas: ${result?.error}")
                    false
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao criar tabelas: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao criar tabelas: ${e.message}")
            false
        }
    }

    suspend fun getExistingSplit(comandaId: Int): PaymentSplit? {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== BUSCANDO DIVISÃO EXISTENTE ===")
            LogHelper.d("Comanda ID: $comandaId")

            val response = NetworkManager.apiService.getExistingSplit(
                comandaId = comandaId,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()
                if (result?.success == true && result.data != null) {
                    LogHelper.d("✅ Divisão existente encontrada")

                    // Converte os dados para PaymentSplit
                    val splitData = result.data
                    val split = PaymentSplit(
                        comandaId = splitData.comandaId,
                        totalParcelas = splitData.totalParcelas,
                        valorTotal = splitData.valorTotal,
                        parcelasPageas = splitData.parcelasPageas,
                        valorPago = splitData.valorPago,
                        status = SplitStatus.values()[splitData.status]
                    )

                    // Busca as parcelas
                    val parcelas = getSplitItems(comandaId)
                    split.parcelas.addAll(parcelas)

                    split
                } else {
                    LogHelper.d("ℹ️ Nenhuma divisão existente encontrada")
                    null
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao buscar divisão: ${response.code()}")
                null
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao buscar divisão: ${e.message}")
            null
        }
    }

    suspend fun getSplitItems(comandaId: Int): List<PaymentSplitItem> {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            val response = NetworkManager.apiService.getSplitItems(
                comandaId = comandaId,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()
                if (result?.success == true && result.items != null) {
                    result.items.map { item ->
                        PaymentSplitItem(
                            parcelaNumero = item.parcelaNumero,
                            valor = item.valor,
                            status = PaymentStatus.values()[item.status],
                            formaPagamento = if (item.formaPagamento > 0) PaymentMethod.values().find { it.statusCode == item.formaPagamento } else null,
                            dataPagamento = item.dataPagamento,
                            cieloResponse = item.cieloResponse
                        )
                    }
                } else {
                    emptyList()
                }
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao buscar parcelas: ${e.message}")
            emptyList()
        }
    }

    suspend fun saveSplitItemImmediate(comandaId: Int, parcela: PaymentSplitItem, totalParcelas: Int, valorTotal: Double): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== SALVANDO PARCELA IMEDIATAMENTE ===")
            LogHelper.d("Comanda ID: $comandaId")
            LogHelper.d("Parcela: ${parcela.parcelaNumero}")
            LogHelper.d("Valor: ${parcela.valor}")
            LogHelper.d("Status: ${parcela.status}")

            val response = NetworkManager.apiService.saveSplitItemImmediate(
                comandaId = comandaId,
                parcelaNumero = parcela.parcelaNumero,
                valor = parcela.valor,
                formaPagamento = parcela.formaPagamento?.statusCode ?: 6,
                status = parcela.status.ordinal,
                dataPagamento = parcela.dataPagamento ?: "",
                cieloResponse = parcela.cieloResponse ?: "",
                totalParcelas = totalParcelas,
                valorTotal = valorTotal,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()
                if (result?.success == true) {
                    LogHelper.d("✅ Parcela salva imediatamente: ${result.message}")
                    true
                } else {
                    LogHelper.e("❌ Erro ao salvar parcela imediatamente: ${result?.error}")
                    false
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao salvar parcela imediatamente: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao salvar parcela imediatamente: ${e.message}")
            false
        }
    }

    suspend fun deleteSplitItems(comandaId: Int): Boolean {
        return try {
            val dbConnection = currentDbConnection ?: throw Exception("Conexão não estabelecida")

            LogHelper.d("=== DELETANDO PARCELAS ANTIGAS ===")
            LogHelper.d("Comanda ID: $comandaId")

            val response = NetworkManager.apiService.deleteSplitItems(
                comandaId = comandaId,
                host = dbConnection.host,
                port = dbConnection.port,
                database = dbConnection.database,
                username = dbConnection.username,
                password = dbConnection.password
            )

            if (response.isSuccessful) {
                val result = response.body()
                if (result?.success == true) {
                    LogHelper.d("✅ Parcelas antigas deletadas: ${result.message}")
                    true
                } else {
                    LogHelper.e("❌ Erro ao deletar parcelas antigas: ${result?.error}")
                    false
                }
            } else {
                LogHelper.e("❌ Erro HTTP ao deletar parcelas antigas: ${response.code()}")
                false
            }
        } catch (e: Exception) {
            LogHelper.e("❌ Erro ao deletar parcelas antigas: ${e.message}")
            false
        }
    }
}

package com.example.guttycielo.network

import com.example.guttycielo.models.Product
import com.example.guttycielo.models.ProductGroup
import com.example.guttycielo.models.VendedorResponse
import retrofit2.Response
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface ApiService {
    
    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun testConnection(
        @Field("action") action: String = "test_connection",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ApiResponse>
    
    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getCategories(
        @Field("action") action: String = "get_categories",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<CategoriesResponse>
    
    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getProductsByCategory(
        @Field("action") action: String = "get_products_by_category",
        @Field("categoria") categoria: String,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ProductResponse>
    
    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun saveOrder(
        @Field("action") action: String = "save_order",
        @Field("order_data") orderData: String,
        @Field("operador") operador: Int,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getRecentCustomers(
        @Field("action") action: String = "get_recent_customers",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<CustomersResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getAllOrders(
        @Field("action") action: String = "get_all_orders",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<OrdersResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getNextTableNumber(
        @Field("action") action: String = "get_next_table_number",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<NextTableResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun markOrderAsPaid(
        @Field("action") action: String = "mark_order_as_paid",
        @Field("table") table: Int,
        @Field("payment_type") paymentType: String,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getPendingOrders(
        @Field("action") action: String = "get_pending_orders",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String,
        @Field("search_query") searchQuery: String = ""
    ): Response<PendingOrdersResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun updateOrderItems(
        @Field("action") action: String = "update_order_items",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String,
        @Field("table_number") tableNumber: String,
        @Field("customer_name") customerName: String,
        @Field("items") itemsJson: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun markOrderAsPaidByDetails(
        @Field("action") action: String = "mark_order_paid_by_details",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String,
        @Field("table_number") tableNumber: String,
        @Field("customer_name") customerName: String,
        @Field("transaction_id") transactionId: String,
        @Field("payment_type") paymentType: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getVendedor(
        @Field("action") action: String = "get_vendedor",
        @Field("codigo") codigo: String,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<VendedorResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun mergeComandas(
        @Field("action") action: String = "merge_comandas",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String,
        @Field("comanda_origem") comandaOrigem: String,
        @Field("comanda_destino") comandaDestino: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getMergesRecentes(
        @Field("action") action: String = "get_merges_recentes",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<MergesRecentesResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun desfazerMerge(
        @Field("action") action: String = "desfazer_merge",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String,
        @Field("merge_id") mergeId: Int
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getComandasAbertas(
        @Field("action") action: String = "get_comandas_abertas",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ComandasAbertasResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun saveSplitPayment(
        @Field("action") action: String = "save_split_payment",
        @Field("comanda_id") comandaId: Int,
        @Field("total_parcelas") totalParcelas: Int,
        @Field("parcelas_pagas") parcelasPagas: Int,
        @Field("valor_total") valorTotal: Double,
        @Field("valor_pago") valorPago: Double,
        @Field("status") status: Int,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun saveSplitPaymentItem(
        @Field("action") action: String = "save_split_payment_item",
        @Field("comanda_id") comandaId: Int,
        @Field("parcela_numero") parcelaNumero: Int,
        @Field("valor") valor: Double,
        @Field("forma_pagamento") formaPagamento: Int,
        @Field("status") status: Int,
        @Field("data_pagamento") dataPagamento: String,
        @Field("cielo_response") cieloResponse: String,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun markOrderAsPaidWithStatus(
        @Field("action") action: String = "mark_order_paid_with_status",
        @Field("table_number") tableNumber: String,
        @Field("customer_name") customerName: String,
        @Field("transaction_id") transactionId: String,
        @Field("status") status: Int,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun markOrderAsPrinted(
        @Field("action") action: String = "mark_order_printed",
        @Field("table_number") tableNumber: String,
        @Field("customer_name") customerName: String,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun createSplitTables(
        @Field("action") action: String = "create_split_tables",
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getExistingSplit(
        @Field("action") action: String = "get_existing_split",
        @Field("comanda_id") comandaId: Int,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ExistingSplitResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun getSplitItems(
        @Field("action") action: String = "get_split_items",
        @Field("comanda_id") comandaId: Int,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<SplitItemsResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun saveSplitItemImmediate(
        @Field("action") action: String = "save_split_item_immediate",
        @Field("comanda_id") comandaId: Int,
        @Field("parcela_numero") parcelaNumero: Int,
        @Field("valor") valor: Double,
        @Field("forma_pagamento") formaPagamento: Int,
        @Field("status") status: Int,
        @Field("data_pagamento") dataPagamento: String,
        @Field("cielo_response") cieloResponse: String,
        @Field("total_parcelas") totalParcelas: Int,
        @Field("valor_total") valorTotal: Double,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ApiResponse>

    @FormUrlEncoded
    @POST("database_api.php")
    suspend fun deleteSplitItems(
        @Field("action") action: String = "delete_split_items",
        @Field("comanda_id") comandaId: Int,
        @Field("host") host: String,
        @Field("port") port: String,
        @Field("database") database: String,
        @Field("username") username: String,
        @Field("password") password: String
    ): Response<ApiResponse>


}

data class ApiResponse(
    val success: Boolean,
    val message: String? = null,
    val error: String? = null
)



data class CategoriesResponse(
    val success: Boolean,
    val data: List<String>? = null,
    val error: String? = null
)

data class Customer(
    val nome: String,
    val comanda: Int
)

data class CustomersResponse(
    val success: Boolean,
    val data: List<Customer>? = null,
    val error: String? = null
)

data class OrderItem(
    val codigo: Int,
    val comanda: Int,
    val data: String,
    val hora: String,
    val codigo_gtin: String,
    val valor: Double,
    val qtde: Double,
    val total: Double,
    val nome: String?,
    val obs: String?
)

data class OrdersResponse(
    val success: Boolean,
    val data: List<OrderItem>? = null,
    val error: String? = null
)

data class NextTableResponse(
    val success: Boolean,
    val data: Int? = null,
    val error: String? = null
)

data class PendingOrdersResponse(
    val success: Boolean,
    val data: List<PendingOrderResponse>? = null,
    val error: String? = null
)

data class PendingOrderResponse(
    val customer_name: String,
    val table_number: String,
    val total_items: Int,
    val total_amount: Double,
    val items: List<PendingOrderItemResponse>
)

data class PendingOrderItemResponse(
    val id: Int,
    val product_code: String,
    val product_name: String,
    val quantity: Int,
    val unit_price: Double,
    val total_price: Double,
    val gtin: String?
)

data class ProductResponse(
    val success: Boolean,
    val data: List<Product>? = null,
    val error: String? = null
)

data class MergesRecentesResponse(
    val success: Boolean,
    val data: List<com.example.guttycielo.models.MergeLog>? = null,
    val error: String? = null
)

data class ComandasAbertasResponse(
    val success: Boolean,
    val data: List<com.example.guttycielo.models.ComandaAberta>? = null,
    val error: String? = null
)

data class ExistingSplitResponse(
    val success: Boolean,
    val data: ExistingSplitData? = null,
    val error: String? = null
)

data class ExistingSplitData(
    val comandaId: Int,
    val totalParcelas: Int,
    val parcelasPageas: Int,
    val valorTotal: Double,
    val valorPago: Double,
    val status: Int
)

data class SplitItemsResponse(
    val success: Boolean,
    val items: List<SplitItemData>? = null,
    val error: String? = null
)

data class SplitItemData(
    val parcelaNumero: Int,
    val valor: Double,
    val status: Int,
    val formaPagamento: Int,
    val dataPagamento: String?,
    val cieloResponse: String?
)



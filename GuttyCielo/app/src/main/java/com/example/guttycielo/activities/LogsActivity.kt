package com.example.guttycielo.activities

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.example.guttycielo.databinding.ActivityLogsBinding
import com.example.guttycielo.utils.LogHelper

class LogsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLogsBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLogsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        loadLogs()
    }

    private fun setupViews() {
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        binding.btnClearLogs.setOnClickListener {
            LogHelper.clearLogs()
            loadLogs()
        }
    }

    private fun loadLogs() {
        val logs = LogHelper.getAllLogs()
        if (logs.isEmpty()) {
            binding.tvLogs.text = "Nenhum log disponível.\n\nTente conectar ao banco para gerar logs."
        } else {
            binding.tvLogs.text = logs.joinToString("\n")
        }
    }

    override fun onResume() {
        super.onResume()
        loadLogs() // Atualiza os logs quando a tela volta ao foco
    }
}

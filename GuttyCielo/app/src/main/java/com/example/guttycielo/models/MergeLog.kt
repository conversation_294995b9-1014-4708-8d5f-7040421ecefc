package com.example.guttycielo.models

import com.google.gson.annotations.SerializedName

/**
 * Modelo para representar um log de merge de comandas
 */
data class MergeLog(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("data_merge")
    val dataMerge: String,
    
    @SerializedName("comanda_origem")
    val comandaOrigem: String,
    
    @SerializedName("comanda_destino")
    val comandaDestino: String,

    @SerializedName("nome_origem")
    val nomeOrigem: String?,

    @SerializedName("nome_destino")
    val nomeDestino: String?,

    @SerializedName("itens_movidos")
    val itensMovidos: String, // JSON com IDs dos itens

    @SerializedName("usuario")
    val usuario: String?,

    @SerializedName("status")
    val status: String // 'ativo', 'desfeito'
) {
    /**
     * Retorna uma descrição formatada do merge com nomes
     */
    fun getDescricao(): String {
        val origemFormatada = if (!nomeOrigem.isNullOrEmpty()) {
            "$comandaOrigem - $nomeOrigem"
        } else {
            comandaOrigem
        }

        val destinoFormatada = if (!nomeDestino.isNullOrEmpty()) {
            "$comandaDestino - $nomeDestino"
        } else {
            comandaDestino
        }

        return "$origemFormatada → $destinoFormatada"
    }

    /**
     * Retorna a data formatada (dd/MM)
     */
    fun getDataFormatada(): String {
        return try {
            // Assume formato: 2024-01-15 14:30:25
            val parts = dataMerge.split(" ")
            if (parts.isNotEmpty()) {
                val datePart = parts[0] // 2024-01-15
                val dateComponents = datePart.split("-")
                if (dateComponents.size >= 3) {
                    "${dateComponents[2]}/${dateComponents[1]}" // dd/MM
                } else {
                    datePart
                }
            } else {
                dataMerge
            }
        } catch (e: Exception) {
            dataMerge
        }
    }

    /**
     * Retorna o horário formatado (HH:MM)
     */
    fun getHorarioFormatado(): String {
        return try {
            // Assume formato: 2024-01-15 14:30:25
            val parts = dataMerge.split(" ")
            if (parts.size >= 2) {
                val timePart = parts[1].substring(0, 5) // HH:MM
                timePart
            } else {
                dataMerge
            }
        } catch (e: Exception) {
            dataMerge
        }
    }

    /**
     * Retorna data e hora formatadas
     */
    fun getDataHoraFormatada(): String {
        return "${getDataFormatada()} às ${getHorarioFormatado()}"
    }
    
    /**
     * Retorna o número de itens movidos
     */
    fun getNumeroItensMovidos(): Int {
        return try {
            // Parse do JSON para contar itens
            val items = itensMovidos.split(",")
            items.size
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * Verifica se o merge pode ser desfeito (últimas 24h)
     */
    fun podeDesfazer(): Boolean {
        return status == "ativo"
    }
}

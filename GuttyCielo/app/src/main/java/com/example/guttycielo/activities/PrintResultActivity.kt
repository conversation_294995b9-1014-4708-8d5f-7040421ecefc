package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.example.guttycielo.R
import com.example.guttycielo.databinding.ActivityPrintResultBinding

class PrintResultActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "PrintResultActivity"
        const val EXTRA_STATUS = "status"
        const val EXTRA_CUSTOMER = "customer"
        const val EXTRA_TABLE = "table"
        const val EXTRA_MESSAGE = "message"
    }
    
    private lateinit var binding: ActivityPrintResultBinding
    private var autoCloseHandler: Handler? = null
    private var autoCloseRunnable: Runnable? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPrintResultBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        val status = intent.getStringExtra(EXTRA_STATUS) ?: "error"
        val customer = intent.getStringExtra(EXTRA_CUSTOMER) ?: ""
        val table = intent.getStringExtra(EXTRA_TABLE) ?: ""
        val message = intent.getStringExtra(EXTRA_MESSAGE) ?: ""
        
        Log.d(TAG, "=== RESULTADO DA IMPRESSÃO ===")
        Log.d(TAG, "Status: $status")
        Log.d(TAG, "Cliente: $customer")
        Log.d(TAG, "Mesa: $table")
        Log.d(TAG, "Mensagem: $message")
        
        setupUI(status, customer, table, message)
    }
    
    private fun setupUI(status: String, customer: String, table: String, message: String) {
        when (status) {
            "success" -> setupSuccessUI(customer, table)
            "no_paper" -> setupNoPaperUI(customer, table)
            "print_error" -> setupErrorUI(customer, table, message)
            else -> setupGenericErrorUI(customer, table)
        }
        
        // Botão OK sempre disponível
        binding.btnOk.setOnClickListener {
            finishAndGoToMain()
        }
    }
    
    private fun setupSuccessUI(customer: String, table: String) {
        binding.iconResult.setImageResource(android.R.drawable.ic_dialog_info)
        binding.iconResult.setColorFilter(getColor(R.color.green))
        
        binding.titleResult.text = "SUCESSO NA IMPRESSÃO!"
        binding.titleResult.setTextColor(getColor(R.color.green))
        
        binding.messageResult.text = "Imprimindo...\n\nCliente: $customer\nMesa: $table"
        
        binding.btnOk.text = "OK"
        binding.btnOk.setBackgroundColor(getColor(R.color.green))
        
        // Auto-close após 5 segundos
        startAutoClose()
    }
    
    private fun setupNoPaperUI(customer: String, table: String) {
        binding.iconResult.setImageResource(android.R.drawable.ic_dialog_alert)
        binding.iconResult.setColorFilter(getColor(R.color.orange))
        
        binding.titleResult.text = "SEM PAPEL NA IMPRESSORA!"
        binding.titleResult.setTextColor(getColor(R.color.orange))
        
        binding.messageResult.text = "Verifique se há papel na impressora e tente novamente.\n\nCliente: $customer\nMesa: $table"
        
        binding.btnOk.text = "ENTENDI"
        binding.btnOk.setBackgroundColor(getColor(R.color.orange))
    }
    
    private fun setupErrorUI(customer: String, table: String, errorMessage: String) {
        binding.iconResult.setImageResource(android.R.drawable.ic_dialog_alert)
        binding.iconResult.setColorFilter(getColor(R.color.red))
        
        binding.titleResult.text = "ERRO NA IMPRESSÃO!"
        binding.titleResult.setTextColor(getColor(R.color.red))
        
        val cleanMessage = errorMessage.replace("java.lang.Throwable: ", "")
        binding.messageResult.text = "Ocorreu um erro durante a impressão:\n\n$cleanMessage\n\nCliente: $customer\nMesa: $table"
        
        binding.btnOk.text = "TENTAR NOVAMENTE"
        binding.btnOk.setBackgroundColor(getColor(R.color.red))
    }
    
    private fun setupGenericErrorUI(customer: String, table: String) {
        binding.iconResult.setImageResource(android.R.drawable.ic_dialog_alert)
        binding.iconResult.setColorFilter(getColor(R.color.red))
        
        binding.titleResult.text = "ERRO DESCONHECIDO!"
        binding.titleResult.setTextColor(getColor(R.color.red))
        
        binding.messageResult.text = "Ocorreu um erro desconhecido durante a impressão.\n\nCliente: $customer\nMesa: $table"
        
        binding.btnOk.text = "OK"
        binding.btnOk.setBackgroundColor(getColor(R.color.red))
    }
    
    private fun startAutoClose() {
        autoCloseHandler = Handler(Looper.getMainLooper())
        autoCloseRunnable = Runnable {
            Log.d(TAG, "⏰ Auto-close ativado - fechando tela de sucesso")
            finishAndGoToMain()
        }
        
        // Fecha automaticamente após 5 segundos
        autoCloseHandler?.postDelayed(autoCloseRunnable!!, 5000)
        
        Log.d(TAG, "⏰ Timer de 5s iniciado para auto-close")
    }
    
    private fun finishAndGoToMain() {
        // Cancela o timer se estiver rodando
        autoCloseHandler?.removeCallbacks(autoCloseRunnable!!)

        // Verifica se deve ir para pagamento após impressão
        val sharedPrefs = getSharedPreferences("payment_after_print", MODE_PRIVATE)
        val shouldGoToPayment = sharedPrefs.getBoolean("should_go_to_payment", false)

        if (shouldGoToPayment) {
            val customerName = sharedPrefs.getString("customer_name", "") ?: ""
            val tableNumber = sharedPrefs.getString("table_number", "") ?: ""

            // Limpa as preferências
            sharedPrefs.edit().clear().apply()

            Log.d(TAG, "🔄 Redirecionando para pagamento após impressão:")
            Log.d(TAG, "   - Cliente: $customerName")
            Log.d(TAG, "   - Mesa: $tableNumber")

            // Vai para tela de pagamento
            val intent = Intent(this, com.example.guttycielo.activities.OrderPaymentActivity::class.java).apply {
                putExtra("customer_name", customerName)
                putExtra("table_number", tableNumber)
                putExtra("is_view_only", false) // Modo pagamento
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
            startActivity(intent)
        } else {
            // Volta para o menu principal
            val intent = Intent(this, com.example.guttycielo.MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(intent)
        }

        finish()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // Limpa o timer
        autoCloseHandler?.removeCallbacks(autoCloseRunnable!!)
    }
}

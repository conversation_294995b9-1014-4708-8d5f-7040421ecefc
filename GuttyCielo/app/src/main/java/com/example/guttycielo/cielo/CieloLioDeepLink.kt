package com.example.guttycielo.cielo

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.util.Base64
import android.util.Log
import com.example.guttycielo.config.CieloLioConfig
import com.example.guttycielo.models.CartItem
import org.json.JSONObject
import java.text.NumberFormat
import java.util.Locale

/**
 * Integração com Cielo LIO via Deep Link
 * 
 * Esta implementação usa Deep Links para se comunicar com o LIO EMULATOR
 * conforme documentação oficial da Cielo.
 * 
 * URI Format: lio://payment?request=BASE64&urlCallback=CALLBACK_URL
 */
class CieloLioDeepLink(private val activity: Activity) {

    companion object {
        private const val TAG = "CieloLioDeepLink"
        const val REQUEST_CODE_PAYMENT = 1001
        
        // URI scheme para Cielo LIO
        const val LIO_SCHEME = "lio://payment"
        
        // Callback scheme conforme documentação oficial
        const val CALLBACK_SCHEME = "order://response"
    }

    /**
     * Interface para callbacks do pagamento
     */
    interface PaymentCallback {
        fun onPaymentStart()
        fun onPaymentSuccess(transactionId: String, amount: Long)
        fun onPaymentCancel()
        fun onPaymentError(error: String)
    }

    /**
     * Inicia o processo de pagamento via Deep Link
     */
    fun startPayment(
        cartItems: List<CartItem>,
        orderReference: String,
        paymentCode: String = "DEBITO_AVISTA",
        customerName: String? = null,
        tableNumber: Int = 1,
        observation: String? = null,
        callback: PaymentCallback
    ) {
        if (!CieloLioConfig.isConfigured()) {
            callback.onPaymentError("Credenciais da Cielo LIO não configuradas")
            return
        }

        try {
            Log.d(TAG, "=== INICIANDO PAGAMENTO CIELO LIO VIA DEEP LINK ===")
            
            val totalInReais = cartItems.sumOf { it.product.precoVenda * it.quantidade }
            val totalInCents = (totalInReais * 100).toLong()

            Log.d(TAG, "Referência: $orderReference")
            Log.d(TAG, "Valor total: ${formatCurrency(totalInReais)}")
            Log.d(TAG, "Valor em centavos: $totalInCents")
            Log.d(TAG, "Cliente: $customerName")
            Log.d(TAG, "Mesa: $tableNumber")

            val requestJson = createPaymentRequest(
                orderReference = orderReference,
                amount = totalInCents,
                paymentCode = paymentCode,
                cartItems = cartItems,
                customerName = customerName,
                tableNumber = tableNumber,
                observation = observation
            )

            val requestBase64 = Base64.encodeToString(requestJson.toByteArray(), Base64.NO_WRAP)
            
            Log.d(TAG, "Request JSON: $requestJson")
            Log.d(TAG, "Request Base64: $requestBase64")

            val checkoutUri = "$LIO_SCHEME?request=$requestBase64&urlCallback=$CALLBACK_SCHEME"
            
            Log.d(TAG, "Deep Link URI: $checkoutUri")

            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(checkoutUri))
            
            try {
                callback.onPaymentStart()
                activity.startActivityForResult(intent, REQUEST_CODE_PAYMENT)
                
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao abrir LIO EMULATOR", e)
                callback.onPaymentError("LIO EMULATOR não encontrado. Instale o LIO EMULATOR para processar pagamentos.")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Erro ao iniciar pagamento", e)
            callback.onPaymentError("Erro ao iniciar pagamento: ${e.message}")
        }
    }

    /**
     * Cria o JSON de request para o pagamento conforme documentação da Cielo
     */
    private fun createPaymentRequest(
        orderReference: String,
        amount: Long,
        paymentCode: String,
        cartItems: List<CartItem>,
        customerName: String?,
        tableNumber: Int,
        observation: String?
    ): String {
        val json = JSONObject()

        try {
            // Dados obrigatórios conforme documentação oficial da Cielo
            json.put("accessToken", CieloLioConfig.ACCESS_TOKEN)
            json.put("clientID", CieloLioConfig.CLIENT_ID)
            json.put("reference", orderReference)
            json.put("value", amount.toString()) // Valor em centavos como string
            json.put("installments", 0)
            json.put("paymentCode", paymentCode) // Forma de pagamento escolhida

            // Email opcional
            customerName?.let {
                // Se não tiver email, usa um padrão baseado no nome
                val email = if (it.contains("@")) it else "${it.replace(" ", "").lowercase()}@cliente.com"
                json.put("email", email)
            }

            // Array de itens conforme documentação
            val itemsArray = org.json.JSONArray()
            cartItems.forEach { item ->
                val itemJson = JSONObject()
                itemJson.put("name", item.product.descricao)
                itemJson.put("quantity", item.quantidade)
                itemJson.put("sku", item.product.codigoInterno.toString())
                itemJson.put("unitOfMeasure", "unidade")
                itemJson.put("unitPrice", (item.product.precoVenda * 100).toInt()) // Preço unitário em centavos
                itemsArray.put(itemJson)
            }
            json.put("items", itemsArray)

            Log.d(TAG, "✅ JSON criado conforme documentação oficial da Cielo:")
            Log.d(TAG, json.toString())

        } catch (e: Exception) {
            Log.e(TAG, "❌ Erro ao criar JSON", e)
        }

        return json.toString()
    }

    /**
     * Processa o resultado do pagamento retornado via callback
     */
    fun handlePaymentResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
        callback: PaymentCallback
    ) {
        if (requestCode != REQUEST_CODE_PAYMENT) {
            return
        }

        Log.d(TAG, "=== RESULTADO DO PAGAMENTO ===")
        Log.d(TAG, "Request Code: $requestCode")
        Log.d(TAG, "Result Code: $resultCode")

        when (resultCode) {
            Activity.RESULT_OK -> {
                // Pagamento aprovado
                val transactionId = data?.getStringExtra("transactionId") ?: ""
                val amount = data?.getLongExtra("amount", 0L) ?: 0L
                
                Log.d(TAG, "✅ Pagamento aprovado")
                Log.d(TAG, "Transaction ID: $transactionId")
                Log.d(TAG, "Amount: $amount")

                callback.onPaymentSuccess(transactionId, amount)
            }

            Activity.RESULT_CANCELED -> {
                // Pagamento cancelado
                Log.d(TAG, "❌ Pagamento cancelado")
                callback.onPaymentCancel()
            }

            else -> {
                // Erro no pagamento
                val errorMessage = data?.getStringExtra("errorMessage") ?: "Erro desconhecido"
                Log.e(TAG, "❌ Erro no pagamento: $errorMessage")
                callback.onPaymentError("Erro no pagamento: $errorMessage")
            }
        }
    }

    /**
     * Processa callback via URI (quando o app é aberto via deep link de retorno)
     */
    fun handleCallbackUri(uri: Uri, callback: PaymentCallback) {
        Log.d(TAG, "=== CALLBACK VIA URI ===")
        Log.d(TAG, "URI: $uri")

        try {
            val status = uri.getQueryParameter("status")
            val transactionId = uri.getQueryParameter("transactionId") ?: ""
            val amount = uri.getQueryParameter("amount")?.toLongOrNull() ?: 0L
            val errorMessage = uri.getQueryParameter("errorMessage")

            when (status?.lowercase()) {
                "approved", "success" -> {
                    Log.d(TAG, "✅ Pagamento aprovado via callback")
                    callback.onPaymentSuccess(transactionId, amount)
                }
                "cancelled", "canceled" -> {
                    Log.d(TAG, "❌ Pagamento cancelado via callback")
                    callback.onPaymentCancel()
                }
                "error", "failed" -> {
                    Log.e(TAG, "❌ Erro no pagamento via callback: $errorMessage")
                    callback.onPaymentError(errorMessage ?: "Erro no pagamento")
                }
                else -> {
                    Log.e(TAG, "❌ Status desconhecido: $status")
                    callback.onPaymentError("Status de pagamento desconhecido: $status")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Erro ao processar callback URI", e)
            callback.onPaymentError("Erro ao processar resultado: ${e.message}")
        }
    }

    private fun formatCurrency(value: Double): String {
        val formatter = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
        return formatter.format(value)
    }
}

package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.guttycielo.adapters.OrderItemsAdapter
import com.example.guttycielo.cielo.CieloLioDeepLink
import com.example.guttycielo.databinding.ActivityOrderPaymentBinding
import com.example.guttycielo.models.CartItem
import com.example.guttycielo.models.PendingOrder
import com.example.guttycielo.models.Product
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.ConnectionManager
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.util.Locale

/**
 * Activity para processar pagamento de pedidos em aberto
 * 
 * Funcionalidades:
 * - Exibir detalhes do pedido
 * - Selecionar forma de pagamento (débito/crédito)
 * - Processar pagamento via Cielo LIO
 * - Marcar pedido como pago após sucesso
 */
class OrderPaymentActivity : AppCompatActivity() {

    private lateinit var binding: ActivityOrderPaymentBinding
    private lateinit var orderItemsAdapter: OrderItemsAdapter
    private lateinit var cieloLioDeepLink: CieloLioDeepLink
    private val currencyFormatter = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
    
    private var customerName: String = ""
    private var tableNumber: String = ""
    private var pendingOrder: PendingOrder? = null
    private var isViewOnly: Boolean = false // Modo apenas visualização

    companion object {
        private const val TAG = "OrderPaymentActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOrderPaymentBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Recebe dados do intent
        customerName = intent.getStringExtra("customer_name") ?: ""
        tableNumber = intent.getStringExtra("table_number") ?: ""
        isViewOnly = intent.getBooleanExtra("view_only", false)

        setupToolbar()
        setupRecyclerView()
        setupButtons()
        initializeCieloLio()
        
        // Carrega detalhes do pedido
        loadOrderDetails()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // Atualiza título baseado no modo
        supportActionBar?.title = if (isViewOnly) {
            "Detalhes - Mesa $tableNumber"
        } else {
            "Pagamento - Mesa $tableNumber"
        }

        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }

    private fun setupRecyclerView() {
        orderItemsAdapter = OrderItemsAdapter(
            items = mutableListOf(),
            onItemClick = { /* Não precisa de ação no click */ }
        )
        
        binding.rvOrderItems.apply {
            layoutManager = LinearLayoutManager(this@OrderPaymentActivity)
            adapter = orderItemsAdapter
        }
    }

    private fun setupButtons() {
        if (isViewOnly) {
            // Modo visualização: esconde elementos de pagamento
            binding.cardPaymentMethod.visibility = android.view.View.GONE
            binding.btnProcessPayment.visibility = android.view.View.GONE
            binding.btnSplitPayment.visibility = android.view.View.GONE
            binding.btnCancel.text = "VOLTAR"

            Log.d(TAG, "🔍 Modo visualização ativado - botões de pagamento ocultos")
        } else {
            // Modo pagamento: configura botões normalmente
            binding.btnProcessPayment.setOnClickListener {
                processPayment()
            }

            binding.btnSplitPayment.setOnClickListener {
                openPaymentSplit()
            }

            Log.d(TAG, "💳 Modo pagamento ativado - botões de pagamento visíveis")
        }



        // Botão para cancelar/voltar
        binding.btnCancel.setOnClickListener {
            finish()
        }
    }

    private fun initializeCieloLio() {
        cieloLioDeepLink = CieloLioDeepLink(this)
    }

    private fun loadOrderDetails() {
        lifecycleScope.launch {
            try {
                binding.progressBar.visibility = android.view.View.VISIBLE
                binding.contentLayout.visibility = android.view.View.GONE
                
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@OrderPaymentActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)
                    
                    Log.d(TAG, "Carregando detalhes do pedido - Cliente: $customerName, Mesa: $tableNumber")
                    
                    // Busca pedidos específicos desta mesa/cliente
                    val searchQuery = if (customerName.isNotEmpty()) {
                        "UPPER(nome) = UPPER('$customerName') AND comanda = $tableNumber"
                    } else {
                        "comanda = $tableNumber"
                    }
                    
                    val orders = DatabaseManager.getPendingOrders(searchQuery)
                    
                    runOnUiThread {
                        binding.progressBar.visibility = android.view.View.GONE
                        binding.contentLayout.visibility = android.view.View.VISIBLE
                        
                        if (orders.isNotEmpty()) {
                            pendingOrder = orders.first()
                            displayOrderDetails(pendingOrder!!)
                        } else {
                            Toast.makeText(this@OrderPaymentActivity, "Pedido não encontrado", Toast.LENGTH_LONG).show()
                            finish()
                        }
                    }
                } else {
                    runOnUiThread {
                        binding.progressBar.visibility = android.view.View.GONE
                        Toast.makeText(this@OrderPaymentActivity, "Erro: Conexão não configurada", Toast.LENGTH_LONG).show()
                        finish()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao carregar detalhes do pedido: ${e.message}", e)
                runOnUiThread {
                    binding.progressBar.visibility = android.view.View.GONE
                    Toast.makeText(this@OrderPaymentActivity, "Erro ao carregar pedido: ${e.message}", Toast.LENGTH_LONG).show()
                    finish()
                }
            }
        }
    }

    private fun displayOrderDetails(order: PendingOrder) {
        // Informações do cabeçalho
        binding.tvCustomerName.text = if (order.customerName.isNotEmpty()) {
            "👤 ${order.customerName}"
        } else {
            "👤 Cliente não informado"
        }
        
        binding.tvTableNumber.text = "🏷️ Mesa/Comanda: ${order.tableNumber}"
        binding.tvTotalItems.text = "📦 ${order.totalItems} ${if (order.totalItems == 1) "item" else "itens"}"
        binding.tvTotalAmount.text = "💰 ${currencyFormatter.format(order.totalAmount)}"

        // Converte itens do pedido para CartItems para usar o adapter existente
        val cartItems = order.items.map { item ->
            CartItem(
                product = Product(
                    codigoInterno = item.id.toLong(),
                    codigoGtin = item.gtin ?: "",
                    descricao = item.productName,
                    descricaoDetalhada = item.productName,
                    grupo = "",
                    categoria = "",
                    precoVenda = item.unitPrice,
                    qtde = 0.0
                ),
                quantidade = item.quantity
            )
        }
        
        orderItemsAdapter.updateItems(cartItems)
    }

    private fun processPayment() {
        val order = pendingOrder ?: return

        // Verifica se é pagamento em dinheiro
        if (binding.rbCash.isChecked) {
            showCashPaymentConfirmation()
            return
        }

        // Determina a forma de pagamento selecionada
        val paymentCode = when {
            binding.rbDebit.isChecked -> "DEBITO_AVISTA"
            binding.rbCredit.isChecked -> "CREDITO_AVISTA"
            binding.rbPix.isChecked -> "PIX"
            else -> "DEBITO_AVISTA" // Padrão
        }

        Log.d(TAG, "=== DEBUG FORMA DE PAGAMENTO ===")
        Log.d(TAG, "rbDebit.isChecked: ${binding.rbDebit.isChecked}")
        Log.d(TAG, "rbCredit.isChecked: ${binding.rbCredit.isChecked}")
        Log.d(TAG, "rbPix.isChecked: ${binding.rbPix.isChecked}")
        Log.d(TAG, "rbCash.isChecked: ${binding.rbCash.isChecked}")
        Log.d(TAG, "paymentCode final: $paymentCode")
        Log.d(TAG, "=================================")

        // Desabilita botão para evitar duplo clique
        binding.btnProcessPayment.isEnabled = false
        binding.btnProcessPayment.text = "PROCESSANDO..."
        
        // Converte para CartItems
        val cartItems = order.items.map { item ->
            CartItem(
                product = Product(
                    codigoInterno = item.id.toLong(),
                    codigoGtin = item.gtin ?: "",
                    descricao = item.productName,
                    descricaoDetalhada = item.productName,
                    grupo = "",
                    categoria = "",
                    precoVenda = item.unitPrice,
                    qtde = 0.0
                ),
                quantidade = item.quantity
            )
        }
        
        // Gera referência do pedido
        val orderReference = "PEDIDO_${System.currentTimeMillis()}_MESA_${order.tableNumber}"
        
        // Salva dados do pedido para usar após o pagamento
        val sharedPrefs = getSharedPreferences("payment_data", MODE_PRIVATE)
        sharedPrefs.edit().apply {
            putInt("table_number", order.tableNumber.toIntOrNull() ?: 0)
            putString("order_reference", orderReference)
            putString("customer_name", order.customerName)
            putString("payment_type", paymentCode) // Salva o tipo de pagamento
            putString("source_activity", "OrderPaymentActivity") // Marca a origem
            apply()
        }
        
        // Cria callback para o pagamento
        val callback = object : CieloLioDeepLink.PaymentCallback {
            override fun onPaymentStart() {
                Log.d(TAG, "💳 Pagamento iniciado via Deep Link")
            }

            override fun onPaymentSuccess(transactionId: String, amount: Long) {
                Log.d(TAG, "✅ Pagamento aprovado: $transactionId")

                // Marca o pedido como pago no banco de dados
                lifecycleScope.launch {
                    try {
                        val success = DatabaseManager.markOrderAsPaid(tableNumber, customerName, transactionId, paymentCode)

                        runOnUiThread {
                            binding.btnProcessPayment.isEnabled = true
                            binding.btnProcessPayment.text = "💳 PAGAR COM CIELO LIO"

                            if (success) {
                                Toast.makeText(this@OrderPaymentActivity, "Pagamento realizado com sucesso!", Toast.LENGTH_SHORT).show()

                                // Vai para a tela de resultado de sucesso
                                val intent = Intent(this@OrderPaymentActivity, PaymentResultActivity::class.java).apply {
                                    putExtra("success", true)
                                    putExtra("transaction_id", transactionId)
                                    putExtra("amount", amount)
                                    putExtra("customer_name", customerName)
                                    putExtra("table_number", tableNumber)
                                }
                                startActivity(intent)
                                finish()
                            } else {
                                Toast.makeText(this@OrderPaymentActivity, "Pagamento aprovado, mas erro ao atualizar banco. Contate o suporte.", Toast.LENGTH_LONG).show()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Erro ao marcar pedido como pago: ${e.message}", e)
                        runOnUiThread {
                            binding.btnProcessPayment.isEnabled = true
                            binding.btnProcessPayment.text = "💳 PAGAR COM CIELO LIO"
                            Toast.makeText(this@OrderPaymentActivity, "Pagamento aprovado, mas erro ao atualizar banco: ${e.message}", Toast.LENGTH_LONG).show()
                        }
                    }
                }
            }

            override fun onPaymentCancel() {
                Log.d(TAG, "❌ Pagamento cancelado pelo usuário")
                runOnUiThread {
                    Toast.makeText(this@OrderPaymentActivity, "Pagamento cancelado. Tente novamente.", Toast.LENGTH_SHORT).show()
                    binding.btnProcessPayment.isEnabled = true
                    binding.btnProcessPayment.text = "💳 PAGAR COM CIELO LIO"
                    // Permanece na tela para tentar novamente
                }
            }

            override fun onPaymentError(error: String) {
                Log.e(TAG, "❌ Erro no pagamento: $error")
                runOnUiThread {
                    Toast.makeText(this@OrderPaymentActivity, "Erro no pagamento: $error\n\nTente novamente.", Toast.LENGTH_LONG).show()
                    binding.btnProcessPayment.isEnabled = true
                    binding.btnProcessPayment.text = "💳 PAGAR COM CIELO LIO"
                    // Permanece na tela para tentar novamente
                }
            }
        }
        
        // Inicia o pagamento via Deep Link
        cieloLioDeepLink.startPayment(
            cartItems = cartItems,
            orderReference = orderReference,
            paymentCode = paymentCode,
            customerName = order.customerName.ifEmpty { null },
            tableNumber = order.tableNumber.toIntOrNull() ?: 1,
            observation = null,
            callback = callback
        )
    }

    private fun showCashPaymentConfirmation() {
        val order = pendingOrder ?: return

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("💵 Pagamento em Dinheiro")
            .setMessage("DESEJA CONFIRMAR O PAGAMENTO EM DINHEIRO?\n\nValor: R$ ${String.format("%.2f", order.totalAmount)}")
            .setPositiveButton("CONFIRMAR") { _, _ ->
                processCashPayment()
            }
            .setNegativeButton("CANCELAR", null)
            .show()
    }

    private fun processCashPayment() {
        val order = pendingOrder ?: return

        // Desabilita o botão para evitar duplo clique
        binding.btnProcessPayment.isEnabled = false
        binding.btnProcessPayment.text = "PROCESSANDO..."

        lifecycleScope.launch {
            try {
                val success = DatabaseManager.markOrderAsPaid(
                    tableNumber = order.tableNumber,
                    customerName = order.customerName,
                    transactionId = "DINHEIRO_${System.currentTimeMillis()}",
                    paymentType = "DINHEIRO"
                )

                runOnUiThread {
                    binding.btnProcessPayment.isEnabled = true
                    binding.btnProcessPayment.text = "💳 PAGAR COM CIELO LIO"

                    if (success) {
                        Toast.makeText(this@OrderPaymentActivity, "Pagamento em dinheiro confirmado!", Toast.LENGTH_SHORT).show()

                        // Vai para a tela de resultado de sucesso
                        val intent = Intent(this@OrderPaymentActivity, PaymentResultActivity::class.java).apply {
                            putExtra(PaymentResultActivity.EXTRA_PAYMENT_STATUS, "success")
                            putExtra(PaymentResultActivity.EXTRA_TRANSACTION_ID, "DINHEIRO_${System.currentTimeMillis()}")
                            putExtra(PaymentResultActivity.EXTRA_AMOUNT, (order.totalAmount * 100).toLong()) // Converte para centavos
                            putExtra(PaymentResultActivity.EXTRA_TABLE_NUMBER, order.tableNumber.toIntOrNull() ?: 0)
                            putExtra(PaymentResultActivity.EXTRA_ORDER_REFERENCE, "DINHEIRO_${order.tableNumber}_${System.currentTimeMillis()}")
                            putExtra("payment_method", "DINHEIRO")
                        }
                        startActivity(intent)
                        finish()
                    } else {
                        Toast.makeText(this@OrderPaymentActivity, "Erro ao confirmar pagamento em dinheiro. Tente novamente.", Toast.LENGTH_LONG).show()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao processar pagamento em dinheiro: ${e.message}", e)
                runOnUiThread {
                    binding.btnProcessPayment.isEnabled = true
                    binding.btnProcessPayment.text = "💳 PAGAR COM CIELO LIO"
                    Toast.makeText(this@OrderPaymentActivity, "Erro ao processar pagamento: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun openPaymentSplit() {
        val order = pendingOrder ?: return

        val intent = Intent(this, PaymentSplitActivity::class.java).apply {
            putExtra(PaymentSplitActivity.EXTRA_PENDING_ORDER, order)
        }
        startActivity(intent)
    }




}

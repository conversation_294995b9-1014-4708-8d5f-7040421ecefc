<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

    <!-- Suporte para diferentes densidades de tela -->
    <supports-screens
        android:smallScreens="true"
        android:normalScreens="true"
        android:largeScreens="true"
        android:xlargeScreens="true"
        android:anyDensity="true"
        android:resizeable="true" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Gutty"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31">

        <!-- Metadado obrigatório para Cielo Smart - Integração via DeepLink -->
        <meta-data
            android:name="cs_integration_type"
            android:value="uri" />
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Gutty">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".activities.ConnectionActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <!-- Activity de login do vendedor -->
        <activity
            android:name=".activities.VendedorLoginActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty"
            android:windowSoftInputMode="stateVisible|adjustResize" />

        <activity
            android:name=".activities.ProductListActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <activity
            android:name=".activities.LogsActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <activity
            android:name=".activities.MenuActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <activity
            android:name=".activities.CartActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <activity
            android:name=".activities.CategoriesActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <activity
            android:name=".activities.ProductsActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <activity
            android:name=".activities.CheckoutActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <activity
            android:name=".activities.OrdersMenuActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <activity
            android:name=".activities.AllOrdersActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty"
            android:windowSoftInputMode="stateHidden" />

        <!-- Activity para receber callback do pagamento Cielo LIO via Deep Link -->
        <activity
            android:name=".activities.PaymentCallbackActivity"
            android:exported="true"
            android:theme="@style/Theme.Gutty"
            android:launchMode="singleTop">

            <!-- Intent filter para deep link de callback -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Esquema de callback conforme documentação oficial -->
                <data android:scheme="order" android:host="response" />
            </intent-filter>
        </activity>

        <!-- Activity para mostrar resultado do pagamento -->
        <activity
            android:name=".activities.PaymentResultActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <!-- Activity para mostrar resultado da impressão -->
        <activity
            android:name=".activities.PrintResultActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <!-- Activity para buscar e gerenciar pagamentos -->
        <activity
            android:name=".activities.PaymentSearchActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty"
            android:windowSoftInputMode="stateHidden" />

        <!-- Activity para processar pagamento de pedidos -->
        <activity
            android:name=".activities.OrderPaymentActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />

        <!-- Activity para unir comandas -->
        <activity
            android:name=".activities.MergeComandasActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty"
            android:windowSoftInputMode="stateHidden" />
        <!-- Activity para divisão de pagamento -->
        <activity
            android:name=".activities.PaymentSplitActivity"
            android:exported="false"
            android:theme="@style/Theme.Gutty" />





    </application>

</manifest>
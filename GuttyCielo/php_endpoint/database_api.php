<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Método não permitido']);
    exit;
}

$action = $_POST['action'] ?? '';
$host = $_POST['host'] ?? '';
$port = $_POST['port'] ?? '5432';
$database = $_POST['database'] ?? '';
$username = $_POST['username'] ?? '';
$password = $_POST['password'] ?? '';

if (empty($action) || empty($host) || empty($database) || empty($username) || empty($password)) {
    echo json_encode(['success' => false, 'error' => 'Parâmetros obrigatórios não fornecidos']);
    exit;
}

function connectToDatabase($host, $port, $database, $username, $password) {
    try {
        $connectionString = "host=$host port=$port dbname=$database user=$username password=$password";
        $connection = pg_connect($connectionString);
        
        if (!$connection) {
            return [false, 'Falha ao conectar com o banco de dados'];
        }
        
        return [$connection, 'Conexão estabelecida com sucesso'];
    } catch (Exception $e) {
        return [false, 'Erro: ' . $e->getMessage()];
    }
}

switch ($action) {
    case 'test_connection':
        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);
        
        if ($connection) {
            // Testa a conexão fazendo uma query simples
            $result = pg_query($connection, "SELECT 1 as test");

            if ($result) {
                pg_free_result($result);

                // Cria tabelas de divisão de pagamento se não existirem
                $createPagamentosDivisao = "
                    CREATE TABLE IF NOT EXISTS pagamentos_divisao (
                        id SERIAL PRIMARY KEY,
                        comanda_id INTEGER NOT NULL,
                        parcela_numero INTEGER NOT NULL,
                        valor DECIMAL(10,2) NOT NULL,
                        forma_pagamento INTEGER NOT NULL,
                        status INTEGER NOT NULL DEFAULT 0,
                        data_pagamento TIMESTAMP,
                        cielo_response TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(comanda_id, parcela_numero)
                    )
                ";
                $resultPagamentos = pg_query($connection, $createPagamentosDivisao);
                if (!$resultPagamentos) {
                    error_log("Erro ao criar tabela pagamentos_divisao: " . pg_last_error($connection));
                }

                $createComandasDivisao = "
                    CREATE TABLE IF NOT EXISTS comandas_divisao (
                        id SERIAL PRIMARY KEY,
                        comanda_id INTEGER NOT NULL UNIQUE,
                        total_parcelas INTEGER NOT NULL,
                        parcelas_pagas INTEGER DEFAULT 0,
                        valor_total DECIMAL(10,2) NOT NULL,
                        valor_pago DECIMAL(10,2) DEFAULT 0,
                        status INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ";
                $resultComandas = pg_query($connection, $createComandasDivisao);
                if (!$resultComandas) {
                    error_log("Erro ao criar tabela comandas_divisao: " . pg_last_error($connection));
                }

                pg_close($connection);
                echo json_encode(['success' => true, 'message' => $message]);
            } else {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao executar query de teste']);
            }
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;
        
    case 'get_categories':
        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Query para buscar apenas categorias principais
            $query = "SELECT DISTINCT categoria FROM produtos_ib WHERE categoria IS NOT NULL ORDER BY categoria";
            $result = pg_query($connection, $query);

            if (!$result) {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao executar query: ' . pg_last_error($connection)]);
                exit;
            }

            $categories = [];
            while ($row = pg_fetch_assoc($result)) {
                $categories[] = $row['categoria'];
            }

            pg_free_result($result);
            pg_close($connection);

            echo json_encode(['success' => true, 'data' => $categories]);
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'get_products_by_category':
        $categoria = $_POST['categoria'] ?? '';

        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Query para buscar produtos por categoria apenas
            $query = "
                SELECT
                    ib.codigo_interno as codigoInterno,
                    p.codigo_gtin as codigoGtin,
                    p.descricao,
                    ib.descricao_detalhada as descricaoDetalhada,
                    ib.grupo,
                    ib.categoria,
                    ib.preco_venda as precoVenda,
                    COALESCE(ou.qtde, 0) as qtde
                FROM produtos_ib ib
                LEFT JOIN produtos p ON ib.codigo_interno = p.codigo_interno
                LEFT JOIN produtos_ou ou ON ib.codigo_interno = ou.codigo_interno
                WHERE ib.categoria = $1
                ORDER BY ib.descricao_detalhada
            ";

            $result = pg_query_params($connection, $query, [$categoria]);

            if (!$result) {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao executar query: ' . pg_last_error($connection)]);
                exit;
            }

            $products = [];
            while ($row = pg_fetch_assoc($result)) {
                $products[] = [
                    'codigoInterno' => (int)$row['codigointerno'],
                    'codigoGtin' => $row['codigogtin'] ?? '',
                    'descricao' => $row['descricao'] ?? '',
                    'descricaoDetalhada' => $row['descricaodetalhada'] ?? '',
                    'grupo' => $row['grupo'] ?? '',
                    'categoria' => $row['categoria'] ?? '',
                    'precoVenda' => (float)$row['precovenda'],
                    'qtde' => (float)$row['qtde']
                ];
            }

            pg_free_result($result);
            pg_close($connection);

            echo json_encode(['success' => true, 'data' => $products]);
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'save_order':
        $orderData = $_POST['order_data'] ?? '';
        $operador = $_POST['operador'] ?? 1; // Código do vendedor

        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Decodifica os dados do pedido
            $order = json_decode($orderData, true);

            if (!$order) {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Dados do pedido inválidos']);
                exit;
            }

            $items = $order['items'] ?? [];
            $customerName = $order['customerName'] ?? null;
            $table = $order['table'] ?? 1;
            $observation = $order['observation'] ?? '';

            // Aplica UPPER e TRIM no nome do cliente
            if (!empty($customerName)) {
                $customerName = strtoupper(trim($customerName));
            }

            $success = true;
            $errorMsg = '';

            // Inicia transação
            pg_query($connection, "BEGIN");

            try {
                foreach ($items as $index => $item) {
                    $produto = $item['product'];
                    $quantidade = $item['quantidade'];
                    $total = $produto['precoVenda'] * $quantidade;

                    $insertQuery = "
                        INSERT INTO pedidos_terminal
                        (comanda, operador, data, hora, codigo_gtin, valor, qtde, total, status, obs, impresso, atendido, item, mesa, nome)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
                    ";

                    $params = [
                        $table, // comanda
                        $operador, // operador (código do vendedor)
                        date('Y-m-d'), // data
                        date('H:i:s'), // hora
                        $produto['codigoGtin'], // codigo_gtin
                        $produto['precoVenda'], // valor
                        $quantidade, // qtde
                        $total, // total
                        0, // status
                        $observation, // obs
                        0, // impresso
                        0, // atendido
                        $index + 1, // item
                        $table, // mesa
                        $customerName // nome
                    ];

                    $result = pg_query_params($connection, $insertQuery, $params);

                    if (!$result) {
                        throw new Exception('Erro ao inserir item: ' . pg_last_error($connection));
                    }
                }

                // Confirma transação
                pg_query($connection, "COMMIT");

            } catch (Exception $e) {
                // Desfaz transação
                pg_query($connection, "ROLLBACK");
                $success = false;
                $errorMsg = $e->getMessage();
            }

            pg_close($connection);

            if ($success) {
                echo json_encode(['success' => true, 'message' => 'Pedido salvo com sucesso']);
            } else {
                echo json_encode(['success' => false, 'error' => $errorMsg]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'get_recent_customers':
        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Busca clientes e comandas com pedidos em aberto (não pagos)
            $query = "
                SELECT DISTINCT
                    COALESCE(nome, '') as nome,
                    comanda
                FROM pedidos_terminal
                WHERE status = 0
                ORDER BY comanda, nome
            ";

            $result = pg_query($connection, $query);

            if (!$result) {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao executar query: ' . pg_last_error($connection)]);
                exit;
            }

            $customers = [];
            while ($row = pg_fetch_assoc($result)) {
                $nome = trim($row['nome']);
                $comanda = (int)$row['comanda'];

                // Cria descrição: se tem nome, mostra "Mesa X - Nome", senão apenas "Mesa X"
                $descricao = empty($nome) ? "Mesa $comanda" : "Mesa $comanda - $nome";

                $customers[] = [
                    'nome' => $nome,
                    'comanda' => $comanda,
                    'descricao' => $descricao
                ];
            }

            pg_free_result($result);
            pg_close($connection);

            echo json_encode(['success' => true, 'data' => $customers]);
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'get_all_orders':
        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Busca apenas pedidos em aberto (não pagos)
            $query = "
                SELECT codigo, comanda, data, hora, codigo_gtin, valor, qtde, total, nome, obs
                FROM pedidos_terminal
                WHERE status = 0
                ORDER BY data DESC, hora DESC
            ";

            $result = pg_query($connection, $query);

            if (!$result) {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao executar query: ' . pg_last_error($connection)]);
                exit;
            }

            $orders = [];
            while ($row = pg_fetch_assoc($result)) {
                $orders[] = [
                    'codigo' => (int)$row['codigo'],
                    'comanda' => (int)$row['comanda'],
                    'data' => $row['data'],
                    'hora' => $row['hora'],
                    'codigo_gtin' => $row['codigo_gtin'],
                    'valor' => (float)$row['valor'],
                    'qtde' => (float)$row['qtde'],
                    'total' => (float)$row['total'],
                    'nome' => $row['nome'],
                    'obs' => $row['obs']
                ];
            }

            pg_free_result($result);
            pg_close($connection);

            echo json_encode(['success' => true, 'data' => $orders]);
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'get_next_table_number':
        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Busca o maior número de comanda/mesa
            $query = "SELECT COALESCE(MAX(comanda), 0) + 1 as next_table FROM pedidos_terminal";

            $result = pg_query($connection, $query);

            if (!$result) {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao executar query: ' . pg_last_error($connection)]);
                exit;
            }

            $row = pg_fetch_assoc($result);
            $nextTable = (int)$row['next_table'];

            pg_free_result($result);
            pg_close($connection);

            echo json_encode(['success' => true, 'data' => $nextTable]);
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'mark_order_as_paid':
        $table = $_POST['table'] ?? '';
        $paymentType = $_POST['payment_type'] ?? 'DEBITO'; // Padrão débito se não informado

        if (empty($table)) {
            echo json_encode(['success' => false, 'error' => 'Número da mesa/comanda é obrigatório']);
            exit;
        }

        // Define status baseado no tipo de pagamento
        $status = 3; // Padrão débito
        switch (strtoupper($paymentType)) {
            case 'DEBITO':
            case 'DEBITO_AVISTA':
                $status = 3;
                break;
            case 'CREDITO':
            case 'CREDITO_AVISTA':
                $status = 4;
                break;
            case 'PIX':
                $status = 5;
                break;
            case 'DINHEIRO':
                $status = 6;
                break;
            default:
                $status = 3; // Padrão débito
        }

        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Atualiza todos os itens do pedido da mesa/comanda com status específico por tipo de pagamento
            $updateQuery = "UPDATE pedidos_terminal SET status = $2 WHERE comanda = $1 AND status = 0";

            $result = pg_query_params($connection, $updateQuery, [$table, $status]);

            if (!$result) {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao atualizar pedido: ' . pg_last_error($connection)]);
                exit;
            }

            $affectedRows = pg_affected_rows($result);
            pg_close($connection);

            if ($affectedRows > 0) {
                echo json_encode(['success' => true, 'message' => "Pedido da mesa/comanda $table marcado como pago ($affectedRows itens atualizados)"]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Nenhum pedido encontrado para esta mesa/comanda ou já está pago']);
            }
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'get_pending_orders':
        $searchQuery = $_POST['search_query'] ?? '';

        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Query para buscar todos os itens de pedidos em aberto com nome do produto
            $baseQuery = "
                SELECT
                    pt.nome as customer_name,
                    pt.comanda as table_number,
                    pt.codigo_gtin,
                    pt.valor as unit_price,
                    pt.qtde as quantity,
                    pt.total as item_total,
                    pt.codigo as item_id,
                    COALESCE(p.descricao, pt.codigo_gtin) as product_name
                FROM pedidos_terminal pt
                LEFT JOIN produtos p ON pt.codigo_gtin = p.codigo_gtin
                WHERE pt.status = 0
            ";

            // Adiciona filtros se fornecidos
            if (!empty($searchQuery)) {
                $baseQuery .= " AND ($searchQuery)";
            }

            $baseQuery .= " ORDER BY comanda, nome";

            // Log da query para debug
            error_log("Query executada: " . $baseQuery);

            $result = pg_query($connection, $baseQuery);

            if (!$result) {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao executar query: ' . pg_last_error($connection)]);
                exit;
            }

            // Log do número de resultados
            $numRows = pg_num_rows($result);
            error_log("Número de linhas encontradas: " . $numRows);

            // Agrupa os resultados apenas por comanda (mesa)
            $groupedOrders = [];
            while ($row = pg_fetch_assoc($result)) {
                $key = $row['table_number']; // Agrupa apenas por mesa/comanda

                if (!isset($groupedOrders[$key])) {
                    $groupedOrders[$key] = [
                        'customer_name' => $row['customer_name'] ?? '',
                        'table_number' => $row['table_number'],
                        'total_items' => 0,
                        'total_amount' => 0.0,
                        'items' => []
                    ];
                } else {
                    // Se já existe o grupo, atualiza o nome do cliente se estiver vazio
                    if (empty($groupedOrders[$key]['customer_name']) && !empty($row['customer_name'])) {
                        $groupedOrders[$key]['customer_name'] = $row['customer_name'];
                    }
                }

                // Log para debug
                error_log("Item encontrado - GTIN: " . $row['codigo_gtin'] . ", Nome: " . $row['product_name']);

                // Adiciona item ao grupo
                $groupedOrders[$key]['items'][] = [
                    'id' => (int)$row['item_id'],
                    'product_code' => $row['codigo_gtin'],
                    'product_name' => $row['product_name'], // Nome real do produto do JOIN
                    'quantity' => (int)$row['quantity'],
                    'unit_price' => (float)$row['unit_price'],
                    'total_price' => (float)$row['item_total'],
                    'gtin' => $row['codigo_gtin']
                ];

                $groupedOrders[$key]['total_items'] += (int)$row['quantity'];
                $groupedOrders[$key]['total_amount'] += (float)$row['item_total'];
            }

            pg_free_result($result);
            pg_close($connection);

            // Converte para array indexado
            $orders = array_values($groupedOrders);

            echo json_encode(['success' => true, 'data' => $orders]);
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'update_order_items':
        $tableNumber = $_POST['table_number'] ?? '';
        $customerName = $_POST['customer_name'] ?? '';
        $itemsJson = $_POST['items'] ?? '';

        if (empty($tableNumber) || empty($itemsJson)) {
            echo json_encode(['success' => false, 'error' => 'Parâmetros obrigatórios não fornecidos']);
            exit;
        }

        $items = json_decode($itemsJson, true);
        if (!$items) {
            echo json_encode(['success' => false, 'error' => 'JSON de itens inválido']);
            exit;
        }

        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Inicia transação
            pg_query($connection, "BEGIN");

            try {
                // Remove todos os itens existentes desta mesa/cliente
                $deleteQuery = "DELETE FROM pedidos_terminal WHERE comanda = $1 AND status = 0";
                if (!empty($customerName)) {
                    $deleteQuery .= " AND nome = $2";
                    $deleteResult = pg_query_params($connection, $deleteQuery, [$tableNumber, $customerName]);
                } else {
                    $deleteResult = pg_query_params($connection, $deleteQuery, [$tableNumber]);
                }

                if (!$deleteResult) {
                    throw new Exception('Erro ao remover itens existentes: ' . pg_last_error($connection));
                }

                // Insere os novos itens
                foreach ($items as $item) {
                    $insertQuery = "
                        INSERT INTO pedidos_terminal
                        (nome, comanda, codigo_gtin, valor, qtde, total, impresso)
                        VALUES ($1, $2, $3, $4, $5, $6, 0)
                    ";

                    $insertResult = pg_query_params($connection, $insertQuery, [
                        $customerName ?: '',
                        $tableNumber,
                        $item['gtin'] ?: $item['product_code'],
                        $item['unit_price'],
                        $item['quantity'],
                        $item['total_price']
                    ]);

                    if (!$insertResult) {
                        throw new Exception('Erro ao inserir item: ' . pg_last_error($connection));
                    }
                }

                // Confirma transação
                pg_query($connection, "COMMIT");
                pg_close($connection);

                echo json_encode(['success' => true, 'message' => 'Itens atualizados com sucesso']);

            } catch (Exception $e) {
                // Desfaz transação em caso de erro
                pg_query($connection, "ROLLBACK");
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'mark_order_paid_by_details':
        $tableNumber = $_POST['table_number'] ?? '';
        $customerName = $_POST['customer_name'] ?? '';
        $transactionId = $_POST['transaction_id'] ?? '';
        $paymentType = $_POST['payment_type'] ?? 'DEBITO'; // Padrão débito se não informado

        if (empty($tableNumber) || empty($transactionId)) {
            echo json_encode(['success' => false, 'error' => 'Parâmetros obrigatórios não fornecidos']);
            exit;
        }

        // Define status baseado no tipo de pagamento
        $status = 3; // Padrão débito
        switch (strtoupper($paymentType)) {
            case 'DEBITO':
            case 'DEBITO_AVISTA':
                $status = 3;
                break;
            case 'CREDITO':
            case 'CREDITO_AVISTA':
                $status = 4;
                break;
            case 'PIX':
                $status = 5;
                break;
            case 'DINHEIRO':
                $status = 6;
                break;
            default:
                $status = 3; // Padrão débito
        }

        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Marca todos os itens desta mesa/cliente como pagos com status específico por tipo de pagamento
            $updateQuery = "UPDATE pedidos_terminal SET status = $2 WHERE comanda = $1";
            $params = [$tableNumber, $status];

            if (!empty($customerName)) {
                $updateQuery .= " AND nome = $3";
                $params[] = $customerName;
            }

            $updateQuery .= " AND status = 0";

            $result = pg_query_params($connection, $updateQuery, $params);

            if ($result) {
                $affectedRows = pg_affected_rows($result);
                pg_close($connection);

                if ($affectedRows > 0) {
                    echo json_encode([
                        'success' => true,
                        'message' => "Pedido marcado como pago. $affectedRows itens atualizados.",
                        'transaction_id' => $transactionId
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'error' => 'Nenhum item encontrado para marcar como pago'
                    ]);
                }
            } else {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao executar query: ' . pg_last_error($connection)]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'merge_comandas':
        $comandaOrigem = $_POST['comanda_origem'] ?? '';
        $comandaDestino = $_POST['comanda_destino'] ?? '';

        if (empty($comandaOrigem) || empty($comandaDestino)) {
            echo json_encode(['success' => false, 'error' => 'Comandas origem e destino são obrigatórias']);
            exit;
        }

        if ($comandaOrigem === $comandaDestino) {
            echo json_encode(['success' => false, 'error' => 'Origem e destino não podem ser iguais']);
            exit;
        }

        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Inicia transação
            pg_query($connection, "BEGIN");

            try {
                // 1. Busca informações das comandas origem e destino
                $origemQuery = "SELECT DISTINCT comanda, nome FROM pedidos_terminal WHERE comanda = $1 AND status = 0 LIMIT 1";
                $origemResult = pg_query_params($connection, $origemQuery, [$comandaOrigem]);

                $destinoQuery = "SELECT DISTINCT comanda, nome FROM pedidos_terminal WHERE comanda = $1 AND status = 0 LIMIT 1";
                $destinoResult = pg_query_params($connection, $destinoQuery, [$comandaDestino]);

                if (!$origemResult || pg_num_rows($origemResult) === 0) {
                    throw new Exception('Comanda origem não encontrada ou não possui itens em aberto');
                }

                $origemData = pg_fetch_assoc($origemResult);
                $nomeOrigem = trim($origemData['nome'] ?? '');

                // Destino pode não existir ainda (nova comanda)
                $nomeDestino = '';
                if ($destinoResult && pg_num_rows($destinoResult) > 0) {
                    $destinoData = pg_fetch_assoc($destinoResult);
                    $nomeDestino = trim($destinoData['nome'] ?? '');
                }

                // 2. Define qual nome usar baseado nas regras
                $nomeParaUsar = '';
                if (!empty($nomeDestino)) {
                    // Destino tem nome - todos herdam o nome do destino
                    $nomeParaUsar = $nomeDestino;
                } elseif (!empty($nomeOrigem)) {
                    // Destino não tem nome, mas origem tem - todos herdam o nome da origem
                    $nomeParaUsar = $nomeOrigem;
                }
                // Se ambos não têm nome, fica vazio (apenas número da comanda)

                // 3. Busca itens da comanda origem
                $selectQuery = "SELECT codigo FROM pedidos_terminal WHERE comanda = $1 AND status = 0";
                $selectResult = pg_query_params($connection, $selectQuery, [$comandaOrigem]);

                if (!$selectResult) {
                    throw new Exception('Erro ao buscar itens da comanda origem');
                }

                $itensMovidos = [];
                while ($row = pg_fetch_assoc($selectResult)) {
                    $itensMovidos[] = $row['codigo'];
                }

                if (empty($itensMovidos)) {
                    throw new Exception('Nenhum item encontrado na comanda origem');
                }

                // 4. Move itens para comanda destino com nome correto
                $updateQuery = "UPDATE pedidos_terminal SET comanda = $1, mesa = $1, nome = $2 WHERE comanda = $3 AND status = 0";
                $updateResult = pg_query_params($connection, $updateQuery, [$comandaDestino, $nomeParaUsar, $comandaOrigem]);

                // 5. Atualiza nome dos itens já existentes no destino (se necessário)
                if (!empty($nomeParaUsar) && !empty($nomeDestino) && $nomeParaUsar !== $nomeDestino) {
                    $updateDestinoQuery = "UPDATE pedidos_terminal SET nome = $1 WHERE comanda = $2 AND status = 0";
                    pg_query_params($connection, $updateDestinoQuery, [$nomeParaUsar, $comandaDestino]);
                }

                if (!$updateResult) {
                    throw new Exception('Erro ao mover itens para comanda destino');
                }

                $itensMovidosCount = pg_affected_rows($updateResult);

                // 3. Cria tabela de log se não existir (com campos para nomes)
                $createLogTable = "
                    CREATE TABLE IF NOT EXISTS merge_log (
                        id SERIAL PRIMARY KEY,
                        data_merge TIMESTAMP DEFAULT NOW(),
                        comanda_origem VARCHAR(50),
                        comanda_destino VARCHAR(50),
                        nome_origem VARCHAR(100),
                        nome_destino VARCHAR(100),
                        itens_movidos TEXT,
                        usuario VARCHAR(100),
                        status VARCHAR(20) DEFAULT 'ativo'
                    )
                ";
                pg_query($connection, $createLogTable);

                // Adiciona colunas se não existirem (para compatibilidade)
                pg_query($connection, "ALTER TABLE merge_log ADD COLUMN IF NOT EXISTS nome_origem VARCHAR(100)");
                pg_query($connection, "ALTER TABLE merge_log ADD COLUMN IF NOT EXISTS nome_destino VARCHAR(100)");

                // 4. Registra o merge no log com nomes originais
                $itensJson = implode(',', $itensMovidos);
                $logQuery = "INSERT INTO merge_log (comanda_origem, comanda_destino, nome_origem, nome_destino, itens_movidos, usuario) VALUES ($1, $2, $3, $4, $5, $6)";
                $logResult = pg_query_params($connection, $logQuery, [$comandaOrigem, $comandaDestino, $nomeOrigem, $nomeDestino, $itensJson, 'app_mobile']);

                if (!$logResult) {
                    throw new Exception('Erro ao registrar log do merge');
                }

                // Confirma transação
                pg_query($connection, "COMMIT");
                pg_close($connection);

                echo json_encode([
                    'success' => true,
                    'message' => "Comandas unidas com sucesso. $itensMovidosCount itens movidos de $comandaOrigem para $comandaDestino."
                ]);

            } catch (Exception $e) {
                // Desfaz transação em caso de erro
                pg_query($connection, "ROLLBACK");
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'get_merges_recentes':
        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Cria tabela de log se não existir
            $createLogTable = "
                CREATE TABLE IF NOT EXISTS merge_log (
                    id SERIAL PRIMARY KEY,
                    data_merge TIMESTAMP DEFAULT NOW(),
                    comanda_origem VARCHAR(50),
                    comanda_destino VARCHAR(50),
                    itens_movidos TEXT,
                    usuario VARCHAR(100),
                    status VARCHAR(20) DEFAULT 'ativo'
                )
            ";
            pg_query($connection, $createLogTable);

            // Busca merges recentes (últimas 24 horas, máximo 20 registros)
            $query = "
                SELECT id, data_merge, comanda_origem, comanda_destino, nome_origem, nome_destino, itens_movidos, usuario, status
                FROM merge_log
                WHERE data_merge >= NOW() - INTERVAL '24 hours'
                ORDER BY data_merge DESC
                LIMIT 20
            ";

            $result = pg_query($connection, $query);

            if ($result) {
                $merges = [];
                while ($row = pg_fetch_assoc($result)) {
                    $merges[] = [
                        'id' => (int)$row['id'],
                        'data_merge' => $row['data_merge'],
                        'comanda_origem' => $row['comanda_origem'],
                        'comanda_destino' => $row['comanda_destino'],
                        'nome_origem' => $row['nome_origem'] ?? '',
                        'nome_destino' => $row['nome_destino'] ?? '',
                        'itens_movidos' => $row['itens_movidos'],
                        'usuario' => $row['usuario'],
                        'status' => $row['status']
                    ];
                }

                pg_close($connection);
                echo json_encode(['success' => true, 'data' => $merges]);
            } else {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao buscar merges recentes: ' . pg_last_error($connection)]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'desfazer_merge':
        $mergeId = (int)($_POST['merge_id'] ?? 0);

        if ($mergeId <= 0) {
            echo json_encode(['success' => false, 'error' => 'ID do merge é obrigatório']);
            exit;
        }

        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Inicia transação
            pg_query($connection, "BEGIN");

            try {
                // 1. Busca dados do merge
                $selectQuery = "SELECT * FROM merge_log WHERE id = $1 AND status = 'ativo'";
                $selectResult = pg_query_params($connection, $selectQuery, [$mergeId]);

                if (!$selectResult || pg_num_rows($selectResult) === 0) {
                    throw new Exception('Merge não encontrado ou já foi desfeito');
                }

                $merge = pg_fetch_assoc($selectResult);
                $comandaOrigem = $merge['comanda_origem'];
                $comandaDestino = $merge['comanda_destino'];
                $nomeOrigem = $merge['nome_origem'] ?? '';
                $nomeDestino = $merge['nome_destino'] ?? '';
                $itensMovidos = explode(',', $merge['itens_movidos']);

                // 2. Move itens de volta para comanda origem COM NOME ORIGINAL
                $updateQuery = "UPDATE pedidos_terminal SET comanda = $1, mesa = $1, nome = $2 WHERE codigo = ANY($3) AND comanda = $4";
                $updateResult = pg_query_params($connection, $updateQuery, [$comandaOrigem, $nomeOrigem, '{' . implode(',', $itensMovidos) . '}', $comandaDestino]);

                // 3. Restaura nome original da comanda destino (se havia itens que ficaram)
                if (!empty($nomeDestino)) {
                    $restoreDestinoQuery = "UPDATE pedidos_terminal SET nome = $1 WHERE comanda = $2 AND status = 0";
                    pg_query_params($connection, $restoreDestinoQuery, [$nomeDestino, $comandaDestino]);
                }

                if (!$updateResult) {
                    throw new Exception('Erro ao mover itens de volta');
                }

                $itensMovidosCount = pg_affected_rows($updateResult);

                // 4. Marca merge como desfeito
                $markQuery = "UPDATE merge_log SET status = 'desfeito' WHERE id = $1";
                $markResult = pg_query_params($connection, $markQuery, [$mergeId]);

                if (!$markResult) {
                    throw new Exception('Erro ao marcar merge como desfeito');
                }

                // Confirma transação
                pg_query($connection, "COMMIT");
                pg_close($connection);

                echo json_encode([
                    'success' => true,
                    'message' => "Merge desfeito com sucesso. $itensMovidosCount itens movidos de volta de $comandaDestino para $comandaOrigem."
                ]);

            } catch (Exception $e) {
                // Desfaz transação em caso de erro
                pg_query($connection, "ROLLBACK");
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'get_comandas_abertas':
        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Busca comandas em aberto (status = 0) agrupadas por comanda/nome
            $query = "
                SELECT
                    comanda,
                    COALESCE(NULLIF(TRIM(nome), ''), '') as nome,
                    COUNT(*) as total_itens,
                    SUM(total) as valor_total
                FROM pedidos_terminal
                WHERE status = 0
                GROUP BY comanda, COALESCE(NULLIF(TRIM(nome), ''), '')
                ORDER BY comanda
            ";

            $result = pg_query($connection, $query);

            if ($result) {
                $comandas = [];
                while ($row = pg_fetch_assoc($result)) {
                    $comandas[] = [
                        'comanda' => $row['comanda'],
                        'nome' => $row['nome'] ?: '',
                        'total_itens' => (int)$row['total_itens'],
                        'valor_total' => (float)$row['valor_total'],
                        'descricao' => $row['nome'] ? "{$row['comanda']} - {$row['nome']}" : $row['comanda']
                    ];
                }

                pg_close($connection);
                echo json_encode(['success' => true, 'data' => $comandas]);
            } else {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao buscar comandas abertas: ' . pg_last_error($connection)]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'test_produtos_table':
        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Verifica se a tabela produtos existe
            $checkTableQuery = "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'produtos')";
            $tableResult = pg_query($connection, $checkTableQuery);
            $tableExists = pg_fetch_result($tableResult, 0, 0);

            if ($tableExists) {
                // Conta quantos produtos existem
                $countQuery = "SELECT COUNT(*) FROM produtos";
                $countResult = pg_query($connection, $countQuery);
                $count = pg_fetch_result($countResult, 0, 0);

                // Pega alguns exemplos
                $sampleQuery = "SELECT codigo_interno, descricao, preco_venda FROM produtos LIMIT 5";
                $sampleResult = pg_query($connection, $sampleQuery);
                $samples = [];
                while ($row = pg_fetch_assoc($sampleResult)) {
                    $samples[] = $row;
                }

                echo json_encode([
                    'success' => true,
                    'table_exists' => true,
                    'count' => (int)$count,
                    'samples' => $samples
                ]);
            } else {
                echo json_encode([
                    'success' => true,
                    'table_exists' => false,
                    'message' => 'Tabela produtos não existe'
                ]);
            }

            pg_close($connection);
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;

    case 'get_vendedor':
        $codigo = $_POST['codigo'] ?? '';

        if (empty($codigo)) {
            echo json_encode(['success' => false, 'error' => 'Código do vendedor é obrigatório']);
            exit;
        }

        list($connection, $message) = connectToDatabase($host, $port, $database, $username, $password);

        if ($connection) {
            // Busca vendedor pelo código
            $query = "SELECT codigo, nome FROM vendedores WHERE codigo = $1 AND (inativo IS NULL OR inativo = 0)";
            $result = pg_query_params($connection, $query, [$codigo]);

            if ($result) {
                if (pg_num_rows($result) > 0) {
                    $vendedor = pg_fetch_assoc($result);

                    // Verifica o controle de ponto do vendedor
                    $controleQuery = "SELECT data_abertura, hora_abertura, data_fechamento, hora_fechamento
                                     FROM caixa_vendedores
                                     WHERE vendedor = $1
                                     ORDER BY data_abertura DESC, hora_abertura DESC
                                     LIMIT 1";
                    $controleResult = pg_query_params($connection, $controleQuery, [$codigo]);

                    if ($controleResult && pg_num_rows($controleResult) > 0) {
                        $controle = pg_fetch_assoc($controleResult);

                        // Verifica se tem abertura
                        if (!empty($controle['data_abertura']) && !empty($controle['hora_abertura'])) {
                            // Tem abertura, verifica se tem fechamento
                            if (!empty($controle['data_fechamento']) && !empty($controle['hora_fechamento'])) {
                                // Tem abertura E fechamento
                                pg_close($connection);
                                echo json_encode([
                                    'success' => false,
                                    'error' => 'Vendedor já registrou o fechamento do ponto no sistema. Entre em contato com o supervisor.'
                                ]);
                                exit;
                            } else {
                                // Tem abertura mas NÃO tem fechamento - LIBERA ACESSO
                                pg_close($connection);
                                echo json_encode([
                                    'success' => true,
                                    'vendedor' => [
                                        'codigo' => $vendedor['codigo'],
                                        'nome' => $vendedor['nome']
                                    ]
                                ]);
                                exit;
                            }
                        } else {
                            // NÃO tem abertura
                            pg_close($connection);
                            echo json_encode([
                                'success' => false,
                                'error' => 'Vendedor não registrou o ponto de abertura no sistema. Registre primeiro no sistema principal.'
                            ]);
                            exit;
                        }
                    } else {
                        // Não encontrou registro de controle
                        pg_close($connection);
                        echo json_encode([
                            'success' => false,
                            'error' => 'Vendedor não registrou o ponto de abertura no sistema. Registre primeiro no sistema principal.'
                        ]);
                        exit;
                    }
                } else {
                    pg_close($connection);
                    echo json_encode(['success' => false, 'error' => 'Vendedor não encontrado ou inativo']);
                }
            } else {
                pg_close($connection);
                echo json_encode(['success' => false, 'error' => 'Erro ao consultar vendedor: ' . pg_last_error($connection)]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => $message]);
        }
        break;



    case 'save_split_payment':
        saveSplitPayment();
        break;

    case 'save_split_payment_item':
        saveSplitPaymentItem();
        break;

    case 'mark_order_paid_with_status':
        markOrderAsPaidWithStatus();
        break;

    case 'create_split_tables':
        createSplitTables();
        break;

    case 'get_existing_split':
        getExistingSplit();
        break;

    case 'get_split_items':
        getSplitItems();
        break;

    case 'save_split_item_immediate':
        saveSplitItemImmediate();
        break;

    case 'delete_split_items':
        deleteSplitItems();
        break;

    default:
        echo json_encode(['success' => false, 'error' => 'Ação não reconhecida']);
        break;
}

function saveSplitPayment() {
    try {
        $comandaId = $_POST['comanda_id'] ?? '';
        $totalParcelas = $_POST['total_parcelas'] ?? '';
        $parcelasPagas = $_POST['parcelas_pagas'] ?? '';
        $valorTotal = $_POST['valor_total'] ?? '';
        $valorPago = $_POST['valor_pago'] ?? '';
        $status = $_POST['status'] ?? '';

        if (empty($comandaId) || empty($totalParcelas) || empty($valorTotal)) {
            echo json_encode(['success' => false, 'error' => 'Dados obrigatórios não fornecidos']);
            return;
        }

        // Pega os dados de conexão do POST
        $host = $_POST['host'] ?? '';
        $port = $_POST['port'] ?? '';
        $database = $_POST['database'] ?? '';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        if (empty($host) || empty($database) || empty($username)) {
            echo json_encode(['success' => false, 'error' => 'Dados de conexão não fornecidos']);
            return;
        }

        // Conecta ao banco
        $connectionString = "host=$host port=$port dbname=$database user=$username password=$password";
        $connection = pg_connect($connectionString);

        if (!$connection) {
            echo json_encode(['success' => false, 'error' => 'Erro ao conectar ao banco de dados']);
            return;
        }

        // Primeiro tenta UPDATE, se não afetar nenhuma linha, faz INSERT
        $updateQuery = "UPDATE comandas_divisao SET
                        total_parcelas = $2,
                        parcelas_pagas = $3,
                        valor_total = $4,
                        valor_pago = $5,
                        status = $6
                        WHERE comanda_id = $1";

        $result = pg_query_params($connection, $updateQuery, [
            $comandaId, $totalParcelas, $parcelasPagas, $valorTotal, $valorPago, $status
        ]);

        if ($result && pg_affected_rows($result) == 0) {
            // Se não atualizou nenhuma linha, insere nova
            pg_free_result($result);
            $insertQuery = "INSERT INTO comandas_divisao (comanda_id, total_parcelas, parcelas_pagas, valor_total, valor_pago, status, created_at)
                           VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)";

            $result = pg_query_params($connection, $insertQuery, [
                $comandaId, $totalParcelas, $parcelasPagas, $valorTotal, $valorPago, $status
            ]);
        }

        if ($result) {
            pg_free_result($result);
            pg_close($connection);
            echo json_encode(['success' => true, 'message' => 'Divisão de pagamento salva com sucesso']);
        } else {
            pg_close($connection);
            echo json_encode(['success' => false, 'error' => 'Erro ao salvar divisão: ' . pg_last_error($connection)]);
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Erro interno: ' . $e->getMessage()]);
    }
}

function saveSplitPaymentItem() {
    try {
        $comandaId = $_POST['comanda_id'] ?? '';
        $parcelaNumero = $_POST['parcela_numero'] ?? '';
        $valor = $_POST['valor'] ?? '';
        $formaPagamento = $_POST['forma_pagamento'] ?? '';
        $status = $_POST['status'] ?? '';
        $dataPagamento = $_POST['data_pagamento'] ?? '';
        $cieloResponse = $_POST['cielo_response'] ?? '';

        if (empty($comandaId) || empty($parcelaNumero) || empty($valor)) {
            echo json_encode(['success' => false, 'error' => 'Dados obrigatórios não fornecidos']);
            return;
        }

        // Pega os dados de conexão do POST
        $host = $_POST['host'] ?? '';
        $port = $_POST['port'] ?? '';
        $database = $_POST['database'] ?? '';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        if (empty($host) || empty($database) || empty($username)) {
            echo json_encode(['success' => false, 'error' => 'Dados de conexão não fornecidos']);
            return;
        }

        // Conecta ao banco
        $connectionString = "host=$host port=$port dbname=$database user=$username password=$password";
        $connection = pg_connect($connectionString);

        if (!$connection) {
            echo json_encode(['success' => false, 'error' => 'Erro ao conectar ao banco de dados']);
            return;
        }

    if ($connection) {
        $query = "INSERT INTO pagamentos_divisao (comanda_id, parcela_numero, valor, forma_pagamento, status, data_pagamento, cielo_response, created_at)
                  VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
                  ON CONFLICT (comanda_id, parcela_numero)
                  DO UPDATE SET
                    valor = EXCLUDED.valor,
                    forma_pagamento = EXCLUDED.forma_pagamento,
                    status = EXCLUDED.status,
                    data_pagamento = EXCLUDED.data_pagamento,
                    cielo_response = EXCLUDED.cielo_response";

        $dataPagamentoParam = !empty($dataPagamento) ? $dataPagamento : null;

        $result = pg_query_params($connection, $query, [
            $comandaId, $parcelaNumero, $valor, $formaPagamento, $status, $dataPagamentoParam, $cieloResponse
        ]);

        if ($result) {
            pg_free_result($result);
            pg_close($connection);
            echo json_encode(['success' => true, 'message' => 'Parcela de pagamento salva com sucesso']);
        } else {
            pg_close($connection);
            echo json_encode(['success' => false, 'error' => 'Erro ao salvar parcela: ' . pg_last_error($connection)]);
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Erro interno: ' . $e->getMessage()]);
    }
}

function markOrderAsPaidWithStatus() {
    try {
        $tableNumber = $_POST['table_number'] ?? '';
        $customerName = $_POST['customer_name'] ?? '';
        $transactionId = $_POST['transaction_id'] ?? '';
        $status = $_POST['status'] ?? '';

        // Log detalhado dos dados recebidos
        error_log("=== MARK ORDER AS PAID WITH STATUS ===");
        error_log("Table Number: $tableNumber");
        error_log("Customer Name: $customerName");
        error_log("Transaction ID: $transactionId");
        error_log("Status: $status");

        if (empty($tableNumber) || empty($customerName) || empty($transactionId) || empty($status)) {
            error_log("ERROR: Dados obrigatórios não fornecidos");
            echo json_encode(['success' => false, 'error' => 'Dados obrigatórios não fornecidos']);
            return;
        }

        // Pega os dados de conexão do POST
        $host = $_POST['host'] ?? '';
        $port = $_POST['port'] ?? '';
        $database = $_POST['database'] ?? '';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        error_log("Connection params: host=$host, port=$port, database=$database, username=$username");

        if (empty($host) || empty($database) || empty($username)) {
            error_log("ERROR: Dados de conexão não fornecidos");
            echo json_encode(['success' => false, 'error' => 'Dados de conexão não fornecidos']);
            return;
        }

        // Conecta ao banco
        $connectionString = "host=$host port=$port dbname=$database user=$username password=$password";
        error_log("Connecting with: $connectionString");
        $connection = pg_connect($connectionString);

        if (!$connection) {
            error_log("ERROR: Erro ao conectar ao banco de dados");
            echo json_encode(['success' => false, 'error' => 'Erro ao conectar ao banco de dados']);
            return;
        }

        error_log("Connected successfully to database");

        // Atualiza o pedido com o status específico (seguindo padrão das outras funções)
        $query = "UPDATE pedidos_terminal
                  SET status = $2
                  WHERE comanda = $1 AND nome = $3 AND status = 0";

        error_log("Executing query: $query");
        error_log("Query params: tableNumber=$tableNumber, status=$status, customerName=$customerName");

        $result = pg_query_params($connection, $query, [
            $tableNumber, $status, $customerName
        ]);

        if ($result) {
            $affectedRows = pg_affected_rows($result);
            error_log("Query executed successfully. Affected rows: $affectedRows");
            pg_free_result($result);
            pg_close($connection);

            if ($affectedRows > 0) {
                error_log("SUCCESS: Pedido marcado como pago com status $status");
                echo json_encode(['success' => true, 'message' => "Pedido marcado como pago com status $status"]);
            } else {
                error_log("WARNING: Nenhum pedido encontrado para atualizar");
                echo json_encode(['success' => false, 'error' => 'Nenhum pedido encontrado para atualizar']);
            }
        } else {
            $error = pg_last_error($connection);
            error_log("ERROR: Erro ao executar query: $error");
            pg_close($connection);
            echo json_encode(['success' => false, 'error' => 'Erro ao marcar pedido como pago: ' . $error]);
        }

    } catch (Exception $e) {
        error_log("EXCEPTION: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Erro interno: ' . $e->getMessage()]);
    }
}

function createSplitTables() {
    try {
        // Pega os dados de conexão do POST
        $host = $_POST['host'] ?? '';
        $port = $_POST['port'] ?? '';
        $database = $_POST['database'] ?? '';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        if (empty($host) || empty($database) || empty($username)) {
            echo json_encode(['success' => false, 'error' => 'Dados de conexão não fornecidos']);
            return;
        }

        // Conecta ao banco
        $connectionString = "host=$host port=$port dbname=$database user=$username password=$password";
        $connection = pg_connect($connectionString);

        if (!$connection) {
            echo json_encode(['success' => false, 'error' => 'Erro ao conectar ao banco de dados']);
            return;
        }

        $errors = [];

        // Cria tabela pagamentos_divisao
        $createPagamentosDivisao = "
            CREATE TABLE IF NOT EXISTS pagamentos_divisao (
                id SERIAL PRIMARY KEY,
                comanda_id INTEGER NOT NULL,
                parcela_numero INTEGER NOT NULL,
                valor DECIMAL(10,2) NOT NULL,
                forma_pagamento INTEGER NOT NULL,
                status INTEGER NOT NULL DEFAULT 0,
                data_pagamento TIMESTAMP,
                cielo_response TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(comanda_id, parcela_numero)
            )
        ";

        $resultPagamentos = pg_query($connection, $createPagamentosDivisao);
        if (!$resultPagamentos) {
            $errors[] = "Erro ao criar tabela pagamentos_divisao: " . pg_last_error($connection);
        }

        // Cria tabela comandas_divisao
        $createComandasDivisao = "
            CREATE TABLE IF NOT EXISTS comandas_divisao (
                id SERIAL PRIMARY KEY,
                comanda_id INTEGER NOT NULL UNIQUE,
                total_parcelas INTEGER NOT NULL,
                parcelas_pagas INTEGER DEFAULT 0,
                valor_total DECIMAL(10,2) NOT NULL,
                valor_pago DECIMAL(10,2) DEFAULT 0,
                status INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ";

        $resultComandas = pg_query($connection, $createComandasDivisao);
        if (!$resultComandas) {
            $errors[] = "Erro ao criar tabela comandas_divisao: " . pg_last_error($connection);
        }

        pg_close($connection);

        if (empty($errors)) {
            echo json_encode(['success' => true, 'message' => 'Tabelas de divisão de pagamento criadas com sucesso']);
        } else {
            echo json_encode(['success' => false, 'error' => implode('; ', $errors)]);
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Erro interno: ' . $e->getMessage()]);
    }
}

function getExistingSplit() {
    try {
        $comandaId = $_POST['comanda_id'] ?? '';

        if (empty($comandaId)) {
            echo json_encode(['success' => false, 'error' => 'Comanda ID não fornecido']);
            return;
        }

        // Pega os dados de conexão do POST
        $host = $_POST['host'] ?? '';
        $port = $_POST['port'] ?? '';
        $database = $_POST['database'] ?? '';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        if (empty($host) || empty($database) || empty($username)) {
            echo json_encode(['success' => false, 'error' => 'Dados de conexão não fornecidos']);
            return;
        }

        // Conecta ao banco
        $connectionString = "host=$host port=$port dbname=$database user=$username password=$password";
        $connection = pg_connect($connectionString);

        if (!$connection) {
            echo json_encode(['success' => false, 'error' => 'Erro ao conectar ao banco de dados']);
            return;
        }

        // Busca divisão em andamento (status = 0) com contagem dinâmica
        // Status pagas: 1=PAGO, 4=DINHEIRO, 6=PIX (todos os status que representam pagamento confirmado)
        $query = "SELECT cd.*,
                         COALESCE((SELECT COUNT(*) FROM pagamentos_divisao WHERE comanda_id = cd.comanda_id), 0) as total_parcelas_real,
                         COALESCE((SELECT COUNT(*) FROM pagamentos_divisao WHERE comanda_id = cd.comanda_id AND status IN (1, 4, 6)), 0) as parcelas_pagas_real,
                         COALESCE((SELECT SUM(valor) FROM pagamentos_divisao WHERE comanda_id = cd.comanda_id AND status IN (1, 4, 6)), 0) as valor_pago_real
                  FROM comandas_divisao cd
                  WHERE cd.comanda_id = $1 AND cd.status = 0
                  LIMIT 1";
        $result = pg_query_params($connection, $query, [$comandaId]);

        if ($result) {
            $row = pg_fetch_assoc($result);

            if ($row) {
                // Divisão encontrada - usa contagem dinâmica
                $data = [
                    'comandaId' => (int)$row['comanda_id'],
                    'totalParcelas' => (int)$row['total_parcelas_real'], // Contagem real da tabela
                    'parcelasPageas' => (int)$row['parcelas_pagas_real'], // Contagem real de pagas
                    'valorTotal' => (float)$row['valor_total'],
                    'valorPago' => (float)$row['valor_pago_real'], // Soma real dos valores pagos
                    'status' => (int)$row['status']
                ];

                echo json_encode(['success' => true, 'data' => $data]);
            } else {
                // Nenhuma divisão encontrada
                echo json_encode(['success' => true, 'data' => null]);
            }

            pg_free_result($result);
        } else {
            echo json_encode(['success' => false, 'error' => 'Erro ao buscar divisão: ' . pg_last_error($connection)]);
        }

        pg_close($connection);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Erro interno: ' . $e->getMessage()]);
    }
}

function getSplitItems() {
    try {
        $comandaId = $_POST['comanda_id'] ?? '';

        if (empty($comandaId)) {
            echo json_encode(['success' => false, 'error' => 'Comanda ID não fornecido']);
            return;
        }

        // Pega os dados de conexão do POST
        $host = $_POST['host'] ?? '';
        $port = $_POST['port'] ?? '';
        $database = $_POST['database'] ?? '';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        if (empty($host) || empty($database) || empty($username)) {
            echo json_encode(['success' => false, 'error' => 'Dados de conexão não fornecidos']);
            return;
        }

        // Conecta ao banco
        $connectionString = "host=$host port=$port dbname=$database user=$username password=$password";
        $connection = pg_connect($connectionString);

        if (!$connection) {
            echo json_encode(['success' => false, 'error' => 'Erro ao conectar ao banco de dados']);
            return;
        }

        $query = "SELECT * FROM pagamentos_divisao WHERE comanda_id = $1 ORDER BY parcela_numero";
        $result = pg_query_params($connection, $query, [$comandaId]);

        if ($result) {
            $items = [];

            while ($row = pg_fetch_assoc($result)) {
                $items[] = [
                    'parcelaNumero' => (int)$row['parcela_numero'],
                    'valor' => (float)$row['valor'],
                    'status' => (int)$row['status'],
                    'formaPagamento' => (int)$row['forma_pagamento'],
                    'dataPagamento' => $row['data_pagamento'],
                    'cieloResponse' => $row['cielo_response']
                ];
            }

            echo json_encode(['success' => true, 'items' => $items]);
            pg_free_result($result);
        } else {
            echo json_encode(['success' => false, 'error' => 'Erro ao buscar parcelas: ' . pg_last_error($connection)]);
        }

        pg_close($connection);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Erro interno: ' . $e->getMessage()]);
    }
}

function saveSplitItemImmediate() {
    try {
        $comandaId = $_POST['comanda_id'] ?? '';
        $parcelaNumero = $_POST['parcela_numero'] ?? '';
        $valor = $_POST['valor'] ?? '';
        $formaPagamento = $_POST['forma_pagamento'] ?? '';
        $status = $_POST['status'] ?? '';
        $dataPagamento = $_POST['data_pagamento'] ?? '';
        $cieloResponse = $_POST['cielo_response'] ?? '';
        $totalParcelas = $_POST['total_parcelas'] ?? '';
        $valorTotal = $_POST['valor_total'] ?? '';

        if (empty($comandaId) || empty($parcelaNumero) || empty($valor)) {
            echo json_encode(['success' => false, 'error' => 'Dados obrigatórios não fornecidos']);
            return;
        }

        // Pega os dados de conexão do POST
        $host = $_POST['host'] ?? '';
        $port = $_POST['port'] ?? '';
        $database = $_POST['database'] ?? '';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        if (empty($host) || empty($database) || empty($username)) {
            echo json_encode(['success' => false, 'error' => 'Dados de conexão não fornecidos']);
            return;
        }

        // Conecta ao banco
        $connectionString = "host=$host port=$port dbname=$database user=$username password=$password";
        $connection = pg_connect($connectionString);

        if (!$connection) {
            echo json_encode(['success' => false, 'error' => 'Erro ao conectar ao banco de dados']);
            return;
        }

    if ($connection) {
        // Inicia transação
        pg_query($connection, "BEGIN");

        try {
            // 1. Cria/atualiza a divisão principal se não existir
            if (!empty($totalParcelas) && !empty($valorTotal)) {
                $queryDivisao = "
                    INSERT INTO comandas_divisao (comanda_id, total_parcelas, parcelas_pagas, valor_total, valor_pago, status, created_at)
                    VALUES ($1, $2, 0, $3, 0, 0, CURRENT_TIMESTAMP)
                    ON CONFLICT (comanda_id)
                    DO NOTHING
                ";

                $resultDivisao = pg_query_params($connection, $queryDivisao, [
                    $comandaId, $totalParcelas, $valorTotal
                ]);

                if (!$resultDivisao) {
                    throw new Exception('Erro ao criar divisão principal: ' . pg_last_error($connection));
                }
            }

            // 2. Salva/atualiza a parcela
            $queryParcela = "
                INSERT INTO pagamentos_divisao (comanda_id, parcela_numero, valor, forma_pagamento, status, data_pagamento, cielo_response, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
                ON CONFLICT (comanda_id, parcela_numero)
                DO UPDATE SET
                    valor = EXCLUDED.valor,
                    forma_pagamento = EXCLUDED.forma_pagamento,
                    status = EXCLUDED.status,
                    data_pagamento = EXCLUDED.data_pagamento,
                    cielo_response = EXCLUDED.cielo_response
            ";

            $dataPagamentoParam = !empty($dataPagamento) ? $dataPagamento : null;

            $resultParcela = pg_query_params($connection, $queryParcela, [
                $comandaId, $parcelaNumero, $valor, $formaPagamento, $status, $dataPagamentoParam, $cieloResponse
            ]);

            if (!$resultParcela) {
                throw new Exception('Erro ao salvar parcela: ' . pg_last_error($connection));
            }

            // 3. Atualiza contadores na divisão principal
            // Status pagas: 1=PAGO, 4=DINHEIRO, 6=PIX
            $queryUpdate = "
                UPDATE comandas_divisao
                SET
                    parcelas_pagas = (SELECT COUNT(*) FROM pagamentos_divisao WHERE comanda_id = $1 AND status IN (1, 4, 6)),
                    valor_pago = (SELECT COALESCE(SUM(valor), 0) FROM pagamentos_divisao WHERE comanda_id = $1 AND status IN (1, 4, 6))
                WHERE comanda_id = $1
            ";

            $resultUpdate = pg_query_params($connection, $queryUpdate, [$comandaId]);

            if (!$resultUpdate) {
                throw new Exception('Erro ao atualizar contadores: ' . pg_last_error($connection));
            }

            // Confirma transação
            pg_query($connection, "COMMIT");

            echo json_encode(['success' => true, 'message' => 'Parcela salva com sucesso']);

        } catch (Exception $e) {
            // Desfaz transação
            pg_query($connection, "ROLLBACK");
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }

        pg_close($connection);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Erro interno: ' . $e->getMessage()]);
    }
}

function deleteSplitItems() {
    try {
        $comandaId = $_POST['comanda_id'] ?? '';

        if (empty($comandaId)) {
            echo json_encode(['success' => false, 'error' => 'Comanda ID não fornecido']);
            return;
        }

        // Pega os dados de conexão do POST
        $host = $_POST['host'] ?? '';
        $port = $_POST['port'] ?? '';
        $database = $_POST['database'] ?? '';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        if (empty($host) || empty($database) || empty($username)) {
            echo json_encode(['success' => false, 'error' => 'Dados de conexão não fornecidos']);
            return;
        }

        // Conecta ao banco
        $connectionString = "host=$host port=$port dbname=$database user=$username password=$password";
        $connection = pg_connect($connectionString);

        if (!$connection) {
            echo json_encode(['success' => false, 'error' => 'Erro ao conectar ao banco de dados']);
            return;
        }

        // Inicia transação para deletar tudo
        pg_query($connection, "BEGIN");

        try {
            // 1. Deleta todas as parcelas desta comanda
            $queryParcelas = "DELETE FROM pagamentos_divisao WHERE comanda_id = $1";
            $resultParcelas = pg_query_params($connection, $queryParcelas, [$comandaId]);

            if (!$resultParcelas) {
                throw new Exception('Erro ao deletar parcelas: ' . pg_last_error($connection));
            }

            $parcelasDeleted = pg_affected_rows($resultParcelas);
            pg_free_result($resultParcelas);

            // 2. Deleta a divisão principal desta comanda
            $queryDivisao = "DELETE FROM comandas_divisao WHERE comanda_id = $1";
            $resultDivisao = pg_query_params($connection, $queryDivisao, [$comandaId]);

            if (!$resultDivisao) {
                throw new Exception('Erro ao deletar divisão principal: ' . pg_last_error($connection));
            }

            $divisaoDeleted = pg_affected_rows($resultDivisao);
            pg_free_result($resultDivisao);

            // Confirma transação
            pg_query($connection, "COMMIT");
            pg_close($connection);

            echo json_encode([
                'success' => true,
                'message' => "Divisão cancelada completamente: $parcelasDeleted parcelas e $divisaoDeleted divisão principal deletadas da comanda $comandaId"
            ]);

        } catch (Exception $e) {
            // Desfaz transação
            pg_query($connection, "ROLLBACK");
            pg_close($connection);
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Erro interno: ' . $e->getMessage()]);
    }
}

?>

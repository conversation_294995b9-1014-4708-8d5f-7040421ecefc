# GuttyCielo - Conexão Direta PostgreSQL

## 📱 Sistema Android com Conexão Direta ao PostgreSQL

### ✅ **Funcionalidades:**
- Conexão direta com banco PostgreSQL
- Tela de configuração de conexão
- Listagem de produtos com busca
- Interface Material Design em XML

### 🔧 **Configuração:**

1. **Preencha os campos na tela de conexão:**
   - **Host**: ***************
   - **Porta**: 5432
   - **Nome do Banco**: 01414955000158
   - **Usuário**: u01414955000158
   - **Senha**: teste123

2. **Estrutura da Tabela Produtos:**
```sql
CREATE TABLE produtos (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    preco DECIMAL(10,2) NOT NULL,
    categoria VARCHAR(100),
    estoque INTEGER DEFAULT 0
);
```

### 📋 **Requisitos:**
- Android API 26+ (Android 8.0)
- Conexão com internet
- Acesso ao servidor PostgreSQL

### 🚀 **Como usar:**
1. Abra o aplicativo
2. Preencha todos os campos de conexão
3. Clique em "Conectar ao Banco PostgreSQL"
4. Aguarde a conexão ser estabelecida
5. Navegue pela lista de produtos

### 🔍 **Troubleshooting:**
- **App fecha ao conectar**: Verifique se todos os campos estão preenchidos
- **Erro de conexão**: Confirme as credenciais do banco
- **Lista vazia**: Verifique se a tabela 'produtos' existe e tem dados

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/light_blue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="Finalizar Pedido"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back" />

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Resumo do Pedido -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Resumo do Pedido"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvCartItems"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_cart" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="16dp"
                android:background="@color/black" />

            <TextView
                android:id="@+id/tvTotal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:textColor="@color/blue"
                android:textSize="24sp"
                android:textStyle="bold"
                tools:text="Total: R$ 15,50" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="16dp"
                android:background="@color/black" />

            <!-- Campos de Associação -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Dados do Cliente"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                >

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilCustomerName"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="Nome do Cliente"
                    >


                    <AutoCompleteTextView
                        android:id="@+id/etCustomerName"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:completionThreshold="1"
                        android:inputType="textPersonName"
                        android:paddingStart="20sp"
                        android:paddingEnd="0sp"
                        />



                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnListCustomers"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:padding="0dp"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    android:insetLeft="0dp"
                    android:insetRight="0dp"
                    android:text="📋"
                    android:textSize="16sp"
                    app:cornerRadius="8dp"

                    />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilTable"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_weight="1"
                    android:hint="Comanda/Mesa">

                    <AutoCompleteTextView
                        android:id="@+id/etTable"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:inputType="number"
                        android:paddingStart="20sp"
                        android:paddingEnd="20sp"
                        android:completionThreshold="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnListTables"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:padding="0dp"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    android:insetLeft="0dp"
                    android:insetRight="0dp"
                    android:text="📋"
                    android:textSize="16sp"
                    app:cornerRadius="8dp" />

            </LinearLayout>

            <!-- Campo de Observação -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilObservation"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="Observações (opcional)">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etObservation"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:lines="3" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Botões de Finalização -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:orientation="vertical">





                <!-- Botão Finalizar Pedido (sem pagamento) -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnFinalize"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="8dp"
                    android:text="FINALIZAR PEDIDO SEM PAGAMENTO"
                    android:backgroundTint="@color/blue"
                    android:textSize="16sp"
                    app:cornerRadius="8dp" />

                <!-- Botão Finalizar com Pagamento -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnFinalizeWithPayment"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="8dp"
                    android:text="FINALIZAR COM PAGAMENTO"
                    android:backgroundTint="@color/green"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    app:cornerRadius="8dp" />

                <!-- Botão Remover Todos os Itens -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnRemoveAllItems"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="8dp"
                    android:text="REMOVER TODOS OS ITENS"
                    android:backgroundTint="@color/red"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    app:cornerRadius="8dp"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>

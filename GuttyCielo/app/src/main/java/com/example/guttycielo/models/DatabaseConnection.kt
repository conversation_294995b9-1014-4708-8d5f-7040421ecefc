package com.example.guttycielo.models

data class DatabaseConnection(
    val host: String,
    val port: String = "5432",
    val database: String,
    val username: String,
    val password: String
) {
    fun getConnectionUrl(): String {
        return "***************************************"
    }
}

//<?php
//
//$database = "01414955000158";
//$conexao = pg_connect("host=postgresql-198445-0.cloudclusters.net port=19627 dbname=".$database." user=u01414955000158 password=014158@@");
//if(!$conexao){
//    echo "Erro ao conectar";
//}
//
//?>
<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:rippleColor="?attr/colorPrimary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Cabeçalho com informações principais -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvCustomerName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="👤 João Silva"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tvTableNumber"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🏷️ Mesa/Comanda: 8"
                    android:textSize="14sp"
                    android:textColor="@color/gray" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="end">

                <TextView
                    android:id="@+id/tvTotalItems"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" 10 itens"
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tvTotalAmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" R$ 125,50"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/light_blue" />

            </LinearLayout>

        </LinearLayout>

        <!-- Divisor -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/gray"
            android:alpha="0.3"
            android:layout_marginBottom="12dp" />

        <!-- Lista de itens (resumida) -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=" Itens do pedido:"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tvItemsList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="• 2x Coca-Cola - R$ 10,00\n• 1x Hambúrguer - R$ 25,00\n• 3x Batata Frita - R$ 15,00\n... e mais 4 itens"
            android:textSize="13sp"
            android:textColor="@color/gray"
            android:lineSpacingExtra="2dp"
            android:layout_marginBottom="16dp" />

        <!-- Botões de ação -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnEditOrder"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="VISUALIZAR"
                android:textSize="14sp"
                android:backgroundTint="@color/orange"
                app:cornerRadius="6dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnPrintOrder"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:text="🖨️ IMPRIMIR"
                android:textSize="12sp"
                android:backgroundTint="@color/green"
                app:cornerRadius="6dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnPayOrder"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="PAGAR"
                android:textSize="14sp"
                android:backgroundTint="@color/light_blue"
                app:cornerRadius="6dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

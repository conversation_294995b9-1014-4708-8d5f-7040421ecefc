package com.example.guttycielo.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.guttycielo.databinding.ItemProductGroupBinding
import com.example.guttycielo.models.ProductGroup

class ProductGroupAdapter(
    private val onCategoryClick: (String, String) -> Unit // categoria, grupo
) : RecyclerView.Adapter<ProductGroupAdapter.GroupViewHolder>() {

    private var groups = listOf<ProductGroup>()
    private val expandedGroups = mutableSetOf<String>()

    fun submitList(newGroups: List<ProductGroup>) {
        groups = newGroups
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GroupViewHolder {
        val binding = ItemProductGroupBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return GroupViewHolder(binding)
    }

    override fun onBindViewHolder(holder: GroupViewHolder, position: Int) {
        holder.bind(groups[position])
    }

    override fun getItemCount(): Int = groups.size

    inner class GroupViewHolder(
        private val binding: ItemProductGroupBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(group: ProductGroup) {
            binding.apply {
                tvGroupName.text = group.nome
                
                val isExpanded = expandedGroups.contains(group.nome)
                rvCategories.visibility = if (isExpanded) View.VISIBLE else View.GONE
                
                // Rotaciona o ícone de expansão
                ivExpand.rotation = if (isExpanded) 180f else 0f
                
                // Configura o adapter de grupos (subcategorias)
                if (rvCategories.adapter == null) {
                    rvCategories.layoutManager = LinearLayoutManager(binding.root.context)
                    rvCategories.adapter = CategoryAdapter(group.categorias) { grupo ->
                        onCategoryClick(group.nome, grupo) // categoria principal, grupo (subcategoria)
                    }
                }
                
                // Click para expandir/contrair
                root.setOnClickListener {
                    if (isExpanded) {
                        expandedGroups.remove(group.nome)
                    } else {
                        expandedGroups.add(group.nome)
                    }
                    notifyItemChanged(adapterPosition)
                }
            }
        }
    }
}

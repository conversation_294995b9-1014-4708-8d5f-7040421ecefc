package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.example.guttycielo.MainActivity

/**
 * Activity para receber callbacks de pagamento da Cielo LIO via Deep Link
 * 
 * Esta activity é chamada quando o LIO EMULATOR retorna o resultado do pagamento
 * via deep link: seuapp://response?status=...&transactionId=...
 */
class PaymentCallbackActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "PaymentCallbackActivity"
        
        // Keys para passar dados entre activities
        const val EXTRA_PAYMENT_STATUS = "payment_status"
        const val EXTRA_TRANSACTION_ID = "transaction_id"
        const val EXTRA_AMOUNT = "amount"
        const val EXTRA_ERROR_MESSAGE = "error_message"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        Log.d(TAG, "=== PAYMENT CALLBACK ACTIVITY ===")
        
        // Processa o callback
        handlePaymentCallback()
    }

    private fun handlePaymentCallback() {
        val data = intent.data
        
        if (data != null) {
            Log.d(TAG, "✅ Callback URI recebida: $data")

            // Variáveis para armazenar o resultado
            var status: String? = null
            var transactionId = ""
            var amount = 0L
            var errorMessage: String? = null

            // A Cielo LIO retorna uma resposta em Base64 no parâmetro "response"
            val responseBase64 = data.getQueryParameter("response")

            if (responseBase64 != null) {
                try {
                    // Decodifica o Base64
                    val responseJson = String(android.util.Base64.decode(responseBase64, android.util.Base64.DEFAULT))
                    Log.d(TAG, "✅ Response JSON decodificado: $responseJson")

                    // Parse do JSON de resposta
                    val jsonResponse = org.json.JSONObject(responseJson)

                    // Verifica se é uma resposta de erro (tem campo "code")
                    if (jsonResponse.has("code")) {
                        // Resposta de erro/cancelamento
                        val code = jsonResponse.optInt("code", -1)
                        val reason = jsonResponse.optString("reason", "")

                        Log.d(TAG, "📋 Resposta com code - Code: $code")
                        Log.d(TAG, "📋 Reason: $reason")

                        status = when (code) {
                            0 -> "success" // Code 0 = Sucesso
                            1 -> "no_paper" // Code 1 = Sem papel
                            2 -> "print_error" // Code 2 = Erro de impressão
                            else -> "error" // Outros codes = Erro genérico
                        }

                        errorMessage = reason
                        transactionId = ""
                        amount = 0L

                    } else {
                        // Resposta de sucesso (tem campos do pedido)
                        Log.d(TAG, "✅ Resposta de sucesso")

                        status = "success"

                        // Extrai dados do pagamento bem-sucedido
                        val paidAmount = jsonResponse.optLong("paidAmount", 0L)
                        val orderId = jsonResponse.optString("id", "")
                        val orderReference = jsonResponse.optString("reference", "")

                        // Extrai dados do primeiro pagamento se existir
                        val paymentsArray = jsonResponse.optJSONArray("payments")
                        if (paymentsArray != null && paymentsArray.length() > 0) {
                            val firstPayment = paymentsArray.getJSONObject(0)
                            transactionId = firstPayment.optString("id", "")
                            val authCode = firstPayment.optString("authCode", "")
                            val brand = firstPayment.optString("brand", "")
                            val mask = firstPayment.optString("mask", "")

                            // Verifica statusCode para PIX e outros pagamentos
                            val paymentFields = firstPayment.optJSONObject("paymentFields")
                            val statusCode = paymentFields?.optInt("statusCode", -1) ?: -1

                            Log.d(TAG, "✅ Status Code: $statusCode")
                            Log.d(TAG, "✅ Transaction ID: $transactionId")
                            Log.d(TAG, "✅ Auth Code: $authCode")
                            Log.d(TAG, "✅ Brand: $brand")
                            Log.d(TAG, "✅ Mask: $mask")

                            // Verifica se o pagamento foi realmente aprovado
                            // statusCode = 0 (PIX) ou 1 (Cartão) = Aprovado
                            // statusCode = 2 = Cancelamento
                            when (statusCode) {
                                0, 1 -> {
                                    // Pagamento aprovado (PIX ou Cartão)
                                    status = "success"
                                    Log.d(TAG, "✅ Pagamento aprovado - StatusCode: $statusCode")
                                }
                                2 -> {
                                    // Cancelamento
                                    status = "cancelled"
                                    errorMessage = "Pagamento cancelado"
                                    Log.d(TAG, "❌ Pagamento cancelado - StatusCode: $statusCode")
                                }
                                else -> {
                                    // Erro ou status desconhecido
                                    status = "error"
                                    errorMessage = "Status de pagamento desconhecido: $statusCode"
                                    Log.d(TAG, "❌ Status desconhecido - StatusCode: $statusCode")
                                }
                            }
                        }

                        amount = paidAmount
                        errorMessage = null

                        Log.d(TAG, "✅ Order ID: $orderId")
                        Log.d(TAG, "✅ Reference: $orderReference")
                        Log.d(TAG, "✅ Paid Amount: $paidAmount")
                    }

                    Log.d(TAG, "Status final determinado: $status")

                } catch (e: Exception) {
                    Log.e(TAG, "❌ Erro ao processar response Base64", e)
                    // Fallback para parâmetros diretos
                    status = data.getQueryParameter("status")
                    transactionId = data.getQueryParameter("transactionId") ?: ""
                    amount = data.getQueryParameter("amount")?.toLongOrNull() ?: 0L
                    errorMessage = data.getQueryParameter("errorMessage")

                    Log.d(TAG, "Fallback - Status: $status")
                    Log.d(TAG, "Fallback - Transaction ID: $transactionId")
                    Log.d(TAG, "Fallback - Amount: $amount")
                    Log.d(TAG, "Fallback - Error: $errorMessage")
                }
            } else {
                // Fallback para parâmetros diretos se não houver response
                status = data.getQueryParameter("status")
                transactionId = data.getQueryParameter("transactionId") ?: ""
                amount = data.getQueryParameter("amount")?.toLongOrNull() ?: 0L
                errorMessage = data.getQueryParameter("errorMessage")

                Log.d(TAG, "Sem response Base64 - Status: $status")
                Log.d(TAG, "Sem response Base64 - Transaction ID: $transactionId")
                Log.d(TAG, "Sem response Base64 - Amount: $amount")
                Log.d(TAG, "Sem response Base64 - Error: $errorMessage")
            }

            // Recupera dados do pedido salvos pelo CheckoutActivity ou PaymentSplitActivity
            val sharedPrefs = getSharedPreferences("payment_data", MODE_PRIVATE)
            val splitPrefs = getSharedPreferences("split_payment_data", MODE_PRIVATE)

            // Tenta primeiro dos dados de divisão, depois dos dados normais
            val tableNumber = splitPrefs.getInt("table_number", sharedPrefs.getInt("table_number", 0))
            val orderReference = splitPrefs.getString("order_reference", "") ?: sharedPrefs.getString("order_reference", "") ?: ""
            val customerName = splitPrefs.getString("customer_name", "") ?: sharedPrefs.getString("customer_name", "") ?: ""

            Log.d(TAG, "📋 Dados recuperados:")
            Log.d(TAG, "   - Mesa: $tableNumber")
            Log.d(TAG, "   - Referência: '$orderReference'")
            Log.d(TAG, "   - Cliente: '$customerName'")
            Log.d(TAG, "📋 Dados de divisão:")
            Log.d(TAG, "   - table_number: ${splitPrefs.getInt("table_number", -1)}")
            Log.d(TAG, "   - order_reference: '${splitPrefs.getString("order_reference", "VAZIO")}'")
            Log.d(TAG, "   - customer_name: '${splitPrefs.getString("customer_name", "VAZIO")}'")
            Log.d(TAG, "📋 Dados normais:")
            Log.d(TAG, "   - table_number: ${sharedPrefs.getInt("table_number", -1)}")
            Log.d(TAG, "   - order_reference: '${sharedPrefs.getString("order_reference", "VAZIO")}'")
            Log.d(TAG, "   - customer_name: '${sharedPrefs.getString("customer_name", "VAZIO")}'")

            // Verifica se é um pagamento de divisão (parcela individual)
            // Tenta primeiro pela referência salva, depois pela referência do JSON da Cielo
            var cieloReference = ""
            try {
                // Tenta extrair a referência do JSON da resposta da Cielo
                val responseParam = data.getQueryParameter("response")
                if (!responseParam.isNullOrEmpty()) {
                    val decodedResponse = String(android.util.Base64.decode(responseParam, android.util.Base64.DEFAULT))
                    val jsonResponse = org.json.JSONObject(decodedResponse)
                    cieloReference = jsonResponse.optString("reference", "")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao extrair referência do JSON: ${e.message}")
            }

            val finalReference = if (orderReference.isNotEmpty()) orderReference else cieloReference

            Log.d(TAG, "🔍 Verificando se é divisão:")
            Log.d(TAG, "   - orderReference salva: '$orderReference'")
            Log.d(TAG, "   - cieloReference do JSON: '$cieloReference'")
            Log.d(TAG, "   - finalReference usada: '$finalReference'")

            // Verifica se é callback de impressão
            val printPrefs = getSharedPreferences("print_callback", MODE_PRIVATE)
            val isPrinting = printPrefs.getBoolean("is_printing", false)

            if (isPrinting) {
                Log.d(TAG, "🖨️ Callback de impressão detectado")
                val printCustomer = printPrefs.getString("print_customer", "") ?: ""
                val printTable = printPrefs.getString("print_table", "") ?: ""

                // Limpa a flag de impressão
                printPrefs.edit().clear().apply()

                openPrintResultActivity(status ?: "unknown", printCustomer, printTable, errorMessage ?: "")
                return
            }

            val isSplitPayment = finalReference.contains("SPLIT_")
            Log.d(TAG, "🔍 É divisão? $isSplitPayment")

            // Se é divisão mas não temos os dados salvos, extrai da referência da Cielo
            var finalTableNumber = tableNumber
            var finalCustomerName = customerName

            if (isSplitPayment && tableNumber == 0 && finalReference.contains("MESA_")) {
                try {
                    val mesaPart = finalReference.substringAfter("MESA_").substringBefore("_")
                    finalTableNumber = mesaPart.toIntOrNull() ?: 0
                    Log.d(TAG, "🔍 Mesa extraída da referência: $finalTableNumber")
                } catch (e: Exception) {
                    Log.e(TAG, "Erro ao extrair mesa da referência: ${e.message}")
                }
            }

            if (isSplitPayment) {
                Log.d(TAG, "🔄 Pagamento de divisão detectado - não abrindo PaymentResultActivity")

                // Para pagamentos de divisão, salva o resultado para a PaymentSplitActivity processar
                // Primeiro, recupera os dados da parcela que estava sendo paga
                val splitDataPrefs = getSharedPreferences("split_payment_data", MODE_PRIVATE)
                val parcelaNumero = splitDataPrefs.getInt("parcela_numero", -1)
                val paymentMethodCode = splitDataPrefs.getString("payment_method", "") ?: ""

                Log.d(TAG, "🔍 Tentando recuperar dados da parcela:")
                Log.d(TAG, "   - parcela_numero lido: $parcelaNumero")
                Log.d(TAG, "   - payment_method lido: '$paymentMethodCode'")
                Log.d(TAG, "   - Todos os dados no SharedPreferences:")
                splitDataPrefs.all.forEach { (key, value) ->
                    Log.d(TAG, "     - $key: $value")
                }

                val splitResultPrefs = getSharedPreferences("split_payment_result", MODE_PRIVATE)
                splitResultPrefs.edit().apply {
                    putString("status", status)
                    putString("transaction_id", transactionId)
                    putLong("amount", amount)
                    putString("error_message", errorMessage ?: "")
                    putInt("parcela_numero", parcelaNumero)
                    putString("payment_method", paymentMethodCode)
                    putLong("timestamp", System.currentTimeMillis())
                    apply()
                }

                Log.d(TAG, "💾 Resultado salvo para PaymentSplitActivity processar")
                Log.d(TAG, "   - status: $status")
                Log.d(TAG, "   - transaction_id: $transactionId")
                Log.d(TAG, "   - amount: $amount")
                Log.d(TAG, "   - parcela_numero: $parcelaNumero")
                Log.d(TAG, "   - payment_method: $paymentMethodCode")

                finish()
            } else {
                Log.d(TAG, "💳 Pagamento normal - abrindo PaymentResultActivity")

                // Cria intent para mostrar o resultado do pagamento
                val resultIntent = Intent(this, PaymentResultActivity::class.java).apply {
                    putExtra(PaymentResultActivity.EXTRA_PAYMENT_STATUS, status)
                    putExtra(PaymentResultActivity.EXTRA_TRANSACTION_ID, transactionId)
                    putExtra(PaymentResultActivity.EXTRA_AMOUNT, amount)
                    putExtra(PaymentResultActivity.EXTRA_ERROR_MESSAGE, errorMessage)
                    putExtra(PaymentResultActivity.EXTRA_TABLE_NUMBER, finalTableNumber)
                    putExtra(PaymentResultActivity.EXTRA_ORDER_REFERENCE, finalReference)
                    putExtra("customer_name", finalCustomerName)
                }

                startActivity(resultIntent)
                finish()
            }
            
        } else {
            Log.e(TAG, "Nenhum dado de callback recebido")
            
            // Volta para a MainActivity sem dados (erro no callback)
            val intent = Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(intent)
            finish()
        }
    }

    private fun openPrintResultActivity(status: String, customer: String, table: String, message: String) {
        Log.d(TAG, "🖨️ Abrindo resultado da impressão:")
        Log.d(TAG, "   - Status: $status")
        Log.d(TAG, "   - Cliente: $customer")
        Log.d(TAG, "   - Mesa: $table")
        Log.d(TAG, "   - Mensagem: $message")

        val intent = Intent(this, PrintResultActivity::class.java).apply {
            putExtra(PrintResultActivity.EXTRA_STATUS, status)
            putExtra(PrintResultActivity.EXTRA_CUSTOMER, customer)
            putExtra(PrintResultActivity.EXTRA_TABLE, table)
            putExtra(PrintResultActivity.EXTRA_MESSAGE, message)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }

        startActivity(intent)
        finish()
    }
}

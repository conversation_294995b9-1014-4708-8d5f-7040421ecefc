package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.guttycielo.adapters.CategoryCardAdapter
import com.example.guttycielo.databinding.ActivityCategoriesBinding
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.ConnectionManager
import kotlinx.coroutines.launch

class CategoriesActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCategoriesBinding
    private lateinit var categoryAdapter: CategoryCardAdapter

    // Variáveis para modo de edição
    private var isEditMode = false
    private var tableNumber = ""
    private var customerName = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCategoriesBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Verifica se está em modo de edição
        isEditMode = intent.getBooleanExtra("edit_mode", false)
        tableNumber = intent.getStringExtra("table_number") ?: ""
        customerName = intent.getStringExtra("customer_name") ?: ""

        setupViews()
        loadCategories()
    }

    private fun setupViews() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // Atualiza título baseado no modo
        supportActionBar?.title = if (isEditMode) {
            "Adicionar Item - Mesa $tableNumber"
        } else {
            "Categorias"
        }

        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        categoryAdapter = CategoryCardAdapter { categoria ->
            openProducts(categoria)
        }

        binding.rvCategories.apply {
            layoutManager = LinearLayoutManager(this@CategoriesActivity)
            adapter = categoryAdapter
        }
    }

    private fun loadCategories() {
        showLoading(true)
        
        lifecycleScope.launch {
            try {
                // Recarrega a conexão
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@CategoriesActivity)
                
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)
                } else {
                    Toast.makeText(this@CategoriesActivity, "Conexão perdida. Retornando...", Toast.LENGTH_LONG).show()
                    finish()
                    return@launch
                }
                
                val categories = DatabaseManager.getCategories()

                if (categories.isNotEmpty()) {
                    categoryAdapter.submitList(categories)
                    showEmptyState(false)
                } else {
                    showEmptyState(true)
                }
            } catch (e: Exception) {
                Toast.makeText(this@CategoriesActivity, "Erro ao carregar categorias: ${e.message}", Toast.LENGTH_LONG).show()
                showEmptyState(true)
            } finally {
                showLoading(false)
            }
        }
    }

    private fun openProducts(categoria: String) {
        val intent = Intent(this, ProductsActivity::class.java).apply {
            putExtra("categoria", categoria)
            if (isEditMode) {
                putExtra("edit_mode", true)
                putExtra("table_number", tableNumber)
                putExtra("customer_name", customerName)
            }
        }

        if (isEditMode) {
            startActivityForResult(intent, 100) // Para receber o resultado
        } else {
            startActivity(intent)
        }
    }

    private fun showLoading(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
        binding.rvCategories.visibility = if (show) View.GONE else View.VISIBLE
    }

    private fun showEmptyState(show: Boolean) {
        binding.tvEmptyState.visibility = if (show) View.VISIBLE else View.GONE
        binding.rvCategories.visibility = if (show) View.GONE else View.VISIBLE
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == 100 && resultCode == RESULT_OK && isEditMode) {
            // Produto foi adicionado, retorna para OrderEditActivity
            setResult(RESULT_OK, data)
            finish()
        }
    }
}

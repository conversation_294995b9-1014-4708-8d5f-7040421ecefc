<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp"
    android:background="@color/white">

    <!-- Ícone do resultado -->
    <ImageView
        android:id="@+id/iconResult"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="24dp"
        android:src="@android:drawable/ic_dialog_info"
        android:contentDescription="Ícone do resultado" />

    <!-- Título do resultado -->
    <TextView
        android:id="@+id/titleResult"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="SUCESSO NA IMPRESSÃO!"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/green"
        android:gravity="center"
        android:textAlignment="center" />

    <!-- Mensagem do resultado -->
    <TextView
        android:id="@+id/messageResult"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="Imprimindo...\n\nCliente: João Silva\nMesa: 15"
        android:textSize="16sp"
        android:textColor="@color/gray"
        android:gravity="center"
        android:textAlignment="center"
        android:lineSpacingExtra="4dp" />

    <!-- Botão OK -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnOk"
        android:layout_width="200dp"
        android:layout_height="56dp"
        android:text="OK"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:backgroundTint="@color/green"
        app:cornerRadius="8dp"
        app:elevation="4dp" />

</LinearLayout>

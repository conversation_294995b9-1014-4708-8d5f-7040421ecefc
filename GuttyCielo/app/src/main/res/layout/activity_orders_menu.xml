<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="Pedidos Pendentes"
        app:titleTextColor="@color/white"
        android:backgroundTint="@color/light_blue"
        app:navigationIcon="@drawable/ic_arrow_back" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <!-- Card Pedidos Não Finalizados -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardPendingOrders"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginBottom="16dp"
            android:clickable="true"
            android:focusable="true"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:rippleColor="?attr/colorPrimary">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="20dp"
                android:background="@color/blue_clear"
                >

                <ImageView
                    android:id="@+id/ivPendingIcon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_order_no_finished"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/black" />

                <TextView
                    android:id="@+id/tvPendingTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="Pedidos Não Finalizados"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@+id/tvPendingSubtitle"
                    app:layout_constraintEnd_toStartOf="@+id/ivPendingArrow"
                    app:layout_constraintStart_toEndOf="@+id/ivPendingIcon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <TextView
                    android:id="@+id/tvPendingSubtitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="16dp"
                    android:text="Ver carrinho atual"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/ivPendingArrow"
                    app:layout_constraintStart_toEndOf="@+id/ivPendingIcon"
                    app:layout_constraintTop_toBottomOf="@+id/tvPendingTitle" />

                <ImageView
                    android:id="@+id/ivPendingArrow"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_forward"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/black" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Card Todos os Pedidos -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardAllOrders"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:clickable="true"
            android:focusable="true"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:rippleColor="?attr/colorPrimary">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="20dp"
                android:background="@color/blue_clear"
                >

                <ImageView
                    android:id="@+id/ivAllIcon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/icone_order_no_printed"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/black" />

                <TextView
                    android:id="@+id/tvAllTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="Pedidos Não Impressos"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@+id/tvAllSubtitle"
                    app:layout_constraintEnd_toStartOf="@+id/ivAllArrow"
                    app:layout_constraintStart_toEndOf="@+id/ivAllIcon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <TextView
                    android:id="@+id/tvAllSubtitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="16dp"
                    android:text="Pedidos não pagos"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/ivAllArrow"
                    app:layout_constraintStart_toEndOf="@+id/ivAllIcon"
                    app:layout_constraintTop_toBottomOf="@+id/tvAllTitle" />

                <ImageView
                    android:id="@+id/ivAllArrow"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_forward"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/black" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

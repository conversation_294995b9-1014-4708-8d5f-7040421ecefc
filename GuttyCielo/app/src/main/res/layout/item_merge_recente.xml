<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Cabeçalho com status -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tvMergeStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ATIVO"
                android:textSize="10sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@color/green"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/tvMergeDateTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="15/01 às 14:30"
                android:textSize="12sp"
                android:textColor="@color/gray"
                android:gravity="end" />

        </LinearLayout>

        <!-- Informação principal do merge -->
        <TextView
            android:id="@+id/tvMergeInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Mesa 5 - João → Mesa 3 - Maria"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="4dp" />

        <!-- Detalhes do merge -->
        <TextView
            android:id="@+id/tvMergeDetails"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="3 itens movidos • Usuário: app_mobile"
            android:textSize="12sp"
            android:textColor="@color/gray"
            android:layout_marginBottom="12dp" />

        <!-- Botão Desfazer -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnDesfazer"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:text="DESFAZER UNIÃO"
            android:textSize="14sp"
            android:backgroundTint="@color/red"
            android:textColor="@color/white"
            app:cornerRadius="8dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.guttycielo.adapters.PendingOrdersAdapter
import com.example.guttycielo.databinding.ActivityPaymentSearchBinding
import com.example.guttycielo.models.PendingOrder
import com.example.guttycielo.models.PaymentSplit
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.ConnectionManager
import com.example.guttycielo.utils.CieloPrinter
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.util.Locale

/**
 * Activity para buscar e gerenciar pagamentos de pedidos em aberto
 * 
 * Funcionalidades:
 * - Buscar pedidos por nome do cliente ou número da comanda
 * - Listar pedidos em aberto (impresso = 0)
 * - Editar pedidos (adicionar/remover itens)
 * - Realizar pagamento via Cielo LIO
 */
class PaymentSearchActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPaymentSearchBinding
    private lateinit var pendingOrdersAdapter: PendingOrdersAdapter
    private val pendingOrders = mutableListOf<PendingOrder>()
    private var isViewOnlyMode = false // Modo apenas visualização

    companion object {
        private const val TAG = "PaymentSearchActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPaymentSearchBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Verifica se está em modo apenas visualização
        isViewOnlyMode = intent.getBooleanExtra("view_only_mode", false)

        setupToolbar()
        setupRecyclerView()
        setupSearchListeners()

        // Carrega todos os pedidos em aberto inicialmente
        searchPendingOrders("")
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // Título baseado no modo
        supportActionBar?.title = if (isViewOnlyMode) {
            "Ver Comandas"
        } else {
            "Receber Pagamento"
        }

        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }

    private fun setupRecyclerView() {
        pendingOrdersAdapter = PendingOrdersAdapter(
            orders = pendingOrders,
            onOrderClick = { order ->
                if (isViewOnlyMode) {
                    // Modo visualização: apenas mostra detalhes
                    openOrderDetails(order)
                } else {
                    // Modo pagamento: abre para pagar
                    openOrderPayment(order)
                }
            },
            onEditClick = { order ->
                // Sempre abre detalhes (botão MOSTRAR)
                openOrderDetails(order)
            },
            onPrintClick = { order ->
                // Imprime o pedido
                printOrder(order)
            },
            isViewOnlyMode = isViewOnlyMode // Passa o modo para o adapter
        )

        binding.rvPendingOrders.apply {
            layoutManager = LinearLayoutManager(this@PaymentSearchActivity)
            adapter = pendingOrdersAdapter
        }
    }

    private fun setupSearchListeners() {
        // Busca por nome do cliente
        binding.etCustomerName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                performSearch()
            }
        })

        // Busca por número da comanda
        binding.etTableNumber.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                performSearch()
            }
        })

        // Botão de buscar
        binding.btnSearch.setOnClickListener {
            performSearch()
        }

        // Botão de limpar filtros
        binding.btnClearFilters.setOnClickListener {
            binding.etCustomerName.text?.clear()
            binding.etTableNumber.text?.clear()
            searchPendingOrders("")
        }

        // Botão Unir Comandas (só no modo pagamento)
        if (!isViewOnlyMode) {
            binding.btnUnirComandas.setOnClickListener {
                val intent = Intent(this, MergeComandasActivity::class.java)
                startActivity(intent)
            }
        } else {
            // Esconde o botão no modo visualização
            binding.btnUnirComandas.visibility = android.view.View.GONE
        }
    }

    private fun performSearch() {
        val customerName = binding.etCustomerName.text.toString().trim()
        val tableNumber = binding.etTableNumber.text.toString().trim()

        Log.d(TAG, "=== REALIZANDO BUSCA ===")
        Log.d(TAG, "Nome do cliente: '$customerName'")
        Log.d(TAG, "Número da comanda: '$tableNumber'")

        val searchQuery = when {
            customerName.isNotEmpty() && tableNumber.isNotEmpty() ->
                "UPPER(nome) LIKE UPPER('%$customerName%') AND comanda = $tableNumber"
            customerName.isNotEmpty() ->
                "UPPER(nome) LIKE UPPER('%$customerName%')"
            tableNumber.isNotEmpty() ->
                "comanda = $tableNumber"
            else -> ""
        }

        Log.d(TAG, "Query de busca gerada: '$searchQuery'")
        searchPendingOrders(searchQuery)
    }

    private fun searchPendingOrders(searchQuery: String) {
        lifecycleScope.launch {
            try {
                binding.progressBar.visibility = android.view.View.VISIBLE
                binding.tvNoResults.visibility = android.view.View.GONE
                
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentSearchActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)
                    
                    Log.d(TAG, "Buscando pedidos em aberto com query: $searchQuery")
                    
                    val orders = DatabaseManager.getPendingOrders(searchQuery)

                    Log.d(TAG, "✅ Resposta da API recebida: ${orders.size} pedidos")
                    orders.forEachIndexed { index, order ->
                        Log.d(TAG, "Pedido $index: ${order.customerName} - Mesa ${order.tableNumber} - ${order.totalItems} itens - R$ ${order.totalAmount}")
                    }

                    runOnUiThread {
                        binding.progressBar.visibility = android.view.View.GONE

                        pendingOrders.clear()
                        pendingOrders.addAll(orders)
                        pendingOrdersAdapter.notifyDataSetChanged()

                        if (orders.isEmpty()) {
                            binding.tvNoResults.visibility = android.view.View.VISIBLE
                            binding.tvNoResults.text = if (searchQuery.isEmpty()) {
                                "Nenhum pedido em aberto encontrado"
                            } else {
                                "Nenhum pedido encontrado para os filtros aplicados"
                            }
                        } else {
                            binding.tvNoResults.visibility = android.view.View.GONE
                        }

                        Log.d(TAG, "✅ Interface atualizada com ${orders.size} pedidos")
                    }
                } else {
                    runOnUiThread {
                        binding.progressBar.visibility = android.view.View.GONE
                        Toast.makeText(this@PaymentSearchActivity, "Erro: Conexão não configurada", Toast.LENGTH_LONG).show()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao buscar pedidos: ${e.message}", e)
                runOnUiThread {
                    binding.progressBar.visibility = android.view.View.GONE
                    Toast.makeText(this@PaymentSearchActivity, "Erro ao buscar pedidos: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun openOrderPayment(order: PendingOrder) {
        Log.d(TAG, "Abrindo pagamento para pedido: ${order.customerName} - Mesa ${order.tableNumber}")

        // Primeiro verifica se há divisão de pagamento pendente
        checkForPendingSplit(order)
    }

    private fun checkForPendingSplit(order: PendingOrder) {
        val comandaId = order.tableNumber.toIntOrNull() ?: 0

        if (comandaId == 0) {
            Log.d(TAG, "⚠️ Comanda ID inválido, abrindo pagamento normal")
            openNormalPayment(order)
            return
        }

        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentSearchActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    Log.d(TAG, "🔍 Verificando divisão pendente para comanda $comandaId")
                    val existingSplit = DatabaseManager.getExistingSplit(comandaId)

                    runOnUiThread {
                        if (existingSplit != null) {
                            Log.d(TAG, "📋 Divisão pendente encontrada!")
                            showPendingSplitDialog(order, existingSplit)
                        } else {
                            Log.d(TAG, "✨ Nenhuma divisão pendente - pagamento normal")
                            openNormalPayment(order)
                        }
                    }
                } else {
                    runOnUiThread {
                        openNormalPayment(order)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao verificar divisão pendente: ${e.message}")
                runOnUiThread {
                    openNormalPayment(order)
                }
            }
        }
    }

    private fun openNormalPayment(order: PendingOrder) {
        val intent = Intent(this, OrderPaymentActivity::class.java).apply {
            putExtra("customer_name", order.customerName)
            putExtra("table_number", order.tableNumber)
            putExtra("total_amount", order.totalAmount)
            putExtra("total_items", order.totalItems)
        }
        startActivity(intent)
    }

    private fun showPendingSplitDialog(order: PendingOrder, existingSplit: PaymentSplit) {
        val currencyFormatter = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
        val temParcelasPagas = existingSplit.parcelasPageas > 0

        val message = """
            🔍 DIVISÃO DE PAGAMENTO PENDENTE DETECTADA

            Mesa/Comanda: ${order.tableNumber}
            Cliente: ${order.customerName}

            📊 Status da Divisão:
            • Total de parcelas: ${existingSplit.totalParcelas}
            • Parcelas pagas: ${existingSplit.parcelasPageas}
            • Valor total: ${currencyFormatter.format(existingSplit.valorTotal)}
            • Valor pago: ${currencyFormatter.format(existingSplit.valorPago)}
            • Restante: ${currencyFormatter.format(existingSplit.valorTotal - existingSplit.valorPago)}

            ${if (temParcelasPagas)
                "⚠️ ATENÇÃO: Algumas parcelas já foram pagas. Você deve continuar com a divisão."
            else
                "ℹ️ Nenhuma parcela foi paga ainda. Você pode cancelar a divisão se desejar."
            }
        """.trimIndent()

        val dialogBuilder = AlertDialog.Builder(this)
            .setTitle("📋 Divisão Pendente Encontrada")
            .setMessage(message)
            .setPositiveButton("✅ CONTINUAR DIVISÃO") { _, _ ->
                Log.d(TAG, "Usuário escolheu continuar a divisão")
                openPendingSplit(order, existingSplit)
            }
            .setNeutralButton("❌ CANCELAR") { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)

        // Só mostra opção de novo pagamento se NENHUMA parcela foi paga
        if (!temParcelasPagas) {
            dialogBuilder.setNegativeButton("🗑️ CANCELAR DIVISÃO") { _, _ ->
                Log.d(TAG, "Usuário escolheu cancelar a divisão")
                showCancelSplitConfirmation(order)
            }
        }

        dialogBuilder.show()
    }

    private fun showCancelSplitConfirmation(order: PendingOrder) {
        AlertDialog.Builder(this)
            .setTitle("🗑️ Cancelar Divisão de Pagamento")
            .setMessage("Tem certeza que deseja cancelar a divisão?\n\n⚠️ ATENÇÃO: Todos os dados da divisão serão perdidos permanentemente.\n\nApós cancelar, você poderá fazer um pagamento normal ou criar uma nova divisão.")
            .setPositiveButton("✅ SIM, CANCELAR DIVISÃO") { _, _ ->
                Log.d(TAG, "Usuário confirmou cancelamento da divisão")
                cancelSplitAndOpenNormalPayment(order)
            }
            .setNegativeButton("❌ NÃO CANCELAR") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun cancelSplitAndOpenNormalPayment(order: PendingOrder) {
        val comandaId = order.tableNumber.toIntOrNull() ?: 0

        if (comandaId == 0) {
            Log.e(TAG, "⚠️ Comanda ID inválido para cancelamento")
            openNormalPayment(order)
            return
        }

        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentSearchActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    Log.d(TAG, "🗑️ Cancelando divisão da comanda $comandaId")
                    val success = DatabaseManager.deleteSplitItems(comandaId)

                    runOnUiThread {
                        if (success) {
                            Log.d(TAG, "✅ Divisão cancelada com sucesso")
                            Toast.makeText(this@PaymentSearchActivity, "✅ Divisão cancelada com sucesso", Toast.LENGTH_SHORT).show()
                        } else {
                            Log.e(TAG, "❌ Erro ao cancelar divisão")
                            Toast.makeText(this@PaymentSearchActivity, "⚠️ Erro ao cancelar divisão", Toast.LENGTH_SHORT).show()
                        }

                        // Abre pagamento normal independente do resultado
                        openNormalPayment(order)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao cancelar divisão: ${e.message}")
                runOnUiThread {
                    Toast.makeText(this@PaymentSearchActivity, "⚠️ Erro ao cancelar divisão", Toast.LENGTH_SHORT).show()
                    openNormalPayment(order)
                }
            }
        }
    }

    private fun openPendingSplit(order: PendingOrder, existingSplit: PaymentSplit) {
        Log.d(TAG, "🚀 Abrindo divisão pendente para: ${order.customerName} - Mesa ${order.tableNumber}")

        val intent = Intent(this, PaymentSplitActivity::class.java).apply {
            putExtra("customer_name", order.customerName)
            putExtra("table_number", order.tableNumber)
            putExtra("total_amount", order.totalAmount)
            putExtra("total_items", order.totalItems)
            putExtra("existing_split", true) // Flag para indicar que é divisão existente
        }

        Log.d(TAG, "📋 Iniciando PaymentSplitActivity com existing_split=true")
        startActivity(intent)

        // Fecha a tela de busca para não voltar aqui
        finish()
    }

    private fun openOrderDetails(order: PendingOrder) {
        Log.d(TAG, "Abrindo detalhes para pedido: ${order.customerName} - Mesa ${order.tableNumber}")

        val intent = Intent(this, OrderPaymentActivity::class.java).apply {
            putExtra("customer_name", order.customerName)
            putExtra("table_number", order.tableNumber)
            putExtra("view_only", true) // Modo apenas visualização
        }
        startActivity(intent)
    }

    override fun onResume() {
        super.onResume()
        // Recarrega a lista quando volta para a tela
        performSearch()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 100 && resultCode == RESULT_OK) {
            // Pedido foi editado, recarrega a lista
            performSearch()
        }
    }

    private fun printOrder(order: PendingOrder) {
        try {
            val printer = CieloPrinter(this)
            printer.printOrder(order)
            Toast.makeText(this, "🖨️ Enviando pedido para impressão...", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(this, "❌ Erro ao imprimir: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
}

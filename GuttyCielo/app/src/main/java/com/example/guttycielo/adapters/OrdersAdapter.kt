package com.example.guttycielo.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.guttycielo.databinding.ItemOrderBinding
import com.example.guttycielo.network.OrderItem
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Locale

class OrdersAdapter : ListAdapter<OrderItem, OrdersAdapter.OrderViewHolder>(OrderDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderViewHolder {
        val binding = ItemOrderBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return OrderViewHolder(binding)
    }

    override fun onBindViewHolder(holder: OrderViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class OrderViewHolder(
        private val binding: ItemOrderBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(order: OrderItem) {
            binding.apply {
                val currencyFormat = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
                
                tvOrderCode.text = "Pedido #${order.codigo}"
                
                // Formata data e hora
                try {
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                    val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
                    val date = dateFormat.parse(order.data)
                    val displayDate = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()).format(date!!)
                    tvDateTime.text = "$displayDate ${order.hora}"
                } catch (e: Exception) {
                    tvDateTime.text = "${order.data} ${order.hora}"
                }
                
                // Informações do cliente
                val customerInfo = buildString {
                    if (!order.nome.isNullOrEmpty()) {
                        append("Cliente: ${order.nome}")
                    }
                    if (order.comanda > 0) {
                        if (isNotEmpty()) append(" - ")
                        append("Mesa: ${order.comanda}")
                    }
                }
                tvCustomerInfo.text = customerInfo.ifEmpty { "Cliente não informado" }
                
                // Nome do produto (pode ser truncado se muito longo)
                tvProductName.text = order.codigo_gtin // Pode ser melhorado buscando o nome real
                
                // Quantidade e valor unitário
                tvQuantity.text = "Qtde: ${order.qtde.toInt()} x ${currencyFormat.format(order.valor)}"
                
                // Total
                tvTotal.text = currencyFormat.format(order.total)
                
                // Observação (se houver)
                if (!order.obs.isNullOrEmpty()) {
                    tvObservation.text = "Obs: ${order.obs}"
                    tvObservation.visibility = View.VISIBLE
                } else {
                    tvObservation.visibility = View.GONE
                }
            }
        }
    }

    class OrderDiffCallback : DiffUtil.ItemCallback<OrderItem>() {
        override fun areItemsTheSame(oldItem: OrderItem, newItem: OrderItem): Boolean {
            return oldItem.codigo == newItem.codigo
        }

        override fun areContentsTheSame(oldItem: OrderItem, newItem: OrderItem): Boolean {
            return oldItem == newItem
        }
    }
}

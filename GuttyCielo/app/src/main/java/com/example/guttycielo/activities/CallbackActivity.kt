package com.example.guttycielo.activities

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.guttycielo.R
import com.example.guttycielo.databinding.ActivityCallbackBinding
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.ConnectionManager
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.util.Locale

/**
 * Activity responsável por receber o callback do pagamento da Cielo LIO
 * via Deep Link e processar o resultado do pagamento.
 * 
 * Esta activity é ativada quando o aplicativo da Cielo LIO retorna o resultado
 * do pagamento através do esquema de URL personalizado: meuapp://callback
 */
class CallbackActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCallbackBinding
    
    // Dados do pagamento extraídos da URI de callback
    private var orderId: String? = null
    private var paymentStatus: String? = null
    private var amount: String? = null
    private var reason: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCallbackBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        processPaymentCallback()
    }

    /**
     * Configura os componentes da interface e listeners
     */
    private fun setupViews() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        // Listener para voltar
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        // Listener para tentar novamente (volta para checkout)
        binding.btnTryAgain.setOnClickListener {
            finish() // Volta para a tela anterior (checkout)
        }

        // Listener para voltar ao menu principal
        binding.btnBackToMenu.setOnClickListener {
            // Finaliza todas as activities e volta ao menu
            val intent = Intent(this, MenuActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(intent)
            finish()
        }
    }

    /**
     * Processa o callback do pagamento da Cielo LIO
     * Extrai os parâmetros da URI de deep link e atualiza a interface
     */
    private fun processPaymentCallback() {
        // Obtém a URI que iniciou esta activity
        val data: Uri? = intent.data

        if (data != null) {
            // Extrai os parâmetros da URI de callback da Cielo LIO
            // Os parâmetros podem variar dependendo da versão da Cielo LIO
            orderId = data.getQueryParameter("orderId") ?: data.getQueryParameter("order_id")
            paymentStatus = data.getQueryParameter("status") ?: data.getQueryParameter("payment_status")
            amount = data.getQueryParameter("amount")
            reason = data.getQueryParameter("reason") ?: data.getQueryParameter("errorMessage")

            // Log para debug
            android.util.Log.d("CallbackActivity", "=== CALLBACK RECEBIDO ===")
            android.util.Log.d("CallbackActivity", "URI: $data")
            android.util.Log.d("CallbackActivity", "Order ID: $orderId")
            android.util.Log.d("CallbackActivity", "Status: $paymentStatus")
            android.util.Log.d("CallbackActivity", "Amount: $amount")
            android.util.Log.d("CallbackActivity", "Reason: $reason")

            // Atualiza a interface com base no resultado
            updateUIBasedOnPaymentResult()
            
            // Se o pagamento foi aprovado, atualiza o status no banco
            if (paymentStatus == "approved" || paymentStatus == "success") {
                updateOrderPaymentStatus()
            }
        } else {
            // Caso não tenha dados na URI, mostra erro
            showErrorState("Erro ao processar callback do pagamento")
        }
    }

    /**
     * Atualiza a interface com base no resultado do pagamento
     */
    private fun updateUIBasedOnPaymentResult() {
        when (paymentStatus?.lowercase()) {
            "confirmed", "approved", "success" -> {
                showSuccessState()
            }
            "declined", "failed", "error", "denied" -> {
                showErrorState("Pagamento recusado")
            }
            "cancelled", "canceled", "aborted" -> {
                showCancelledState()
            }
            else -> {
                showUnknownState()
            }
        }

        // Atualiza informações do pedido
        binding.tvOrderId.text = "#$orderId"
        
        // Formata o valor se disponível
        amount?.let { amountStr ->
            try {
                // Converte centavos para reais
                val amountInCents = amountStr.toLong()
                val amountInReais = amountInCents / 100.0
                val formatter = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
                binding.tvAmount.text = formatter.format(amountInReais)
            } catch (e: Exception) {
                binding.tvAmount.text = "R$ --,--"
            }
        } ?: run {
            binding.tvAmount.text = "R$ --,--"
        }

        // Mostra motivo se disponível
        if (!reason.isNullOrEmpty()) {
            binding.layoutReason.visibility = View.VISIBLE
            binding.tvReason.text = reason
        }
    }

    /**
     * Configura interface para pagamento aprovado
     */
    private fun showSuccessState() {
        binding.ivStatusIcon.setImageResource(android.R.drawable.ic_dialog_info)
        binding.ivStatusIcon.setColorFilter(getColor(android.R.color.holo_green_dark))
        binding.tvStatusTitle.text = "Pagamento Aprovado!"
        binding.tvStatusTitle.setTextColor(getColor(android.R.color.holo_green_dark))
        binding.tvStatusDetails.text = "Seu pagamento foi processado com sucesso."
        binding.btnTryAgain.visibility = View.GONE
    }

    /**
     * Configura interface para pagamento recusado/erro
     */
    private fun showErrorState(message: String) {
        binding.ivStatusIcon.setImageResource(android.R.drawable.ic_dialog_alert)
        binding.ivStatusIcon.setColorFilter(getColor(android.R.color.holo_red_dark))
        binding.tvStatusTitle.text = "Pagamento Recusado"
        binding.tvStatusTitle.setTextColor(getColor(android.R.color.holo_red_dark))
        binding.tvStatusDetails.text = message
        binding.btnTryAgain.visibility = View.VISIBLE
    }

    /**
     * Configura interface para pagamento cancelado
     */
    private fun showCancelledState() {
        binding.ivStatusIcon.setImageResource(android.R.drawable.ic_dialog_info)
        binding.ivStatusIcon.setColorFilter(getColor(android.R.color.holo_orange_dark))
        binding.tvStatusTitle.text = "Pagamento Cancelado"
        binding.tvStatusTitle.setTextColor(getColor(android.R.color.holo_orange_dark))
        binding.tvStatusDetails.text = "O pagamento foi cancelado pelo usuário."
        binding.btnTryAgain.visibility = View.VISIBLE
    }

    /**
     * Configura interface para status desconhecido
     */
    private fun showUnknownState() {
        binding.ivStatusIcon.setImageResource(android.R.drawable.ic_dialog_info)
        binding.ivStatusIcon.setColorFilter(getColor(R.color.light_blue))
        binding.tvStatusTitle.text = "Status Desconhecido"
        binding.tvStatusTitle.setTextColor(getColor(R.color.light_blue))
        binding.tvStatusDetails.text = "Não foi possível determinar o status do pagamento."
        binding.btnTryAgain.visibility = View.VISIBLE
    }

    /**
     * Atualiza o status do pedido no banco de dados para "pago" (impresso = 1)
     * quando o pagamento é aprovado
     */
    private fun updateOrderPaymentStatus() {
        orderId?.let { orderIdStr ->
            lifecycleScope.launch {
                try {
                    // Recarrega a conexão
                    val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@CallbackActivity)

                    if (serverUrl != null && dbConnection != null) {
                        NetworkManager.updateBaseUrl(serverUrl)
                        DatabaseManager.setCurrentConnection(dbConnection)

                        // Extrai o número da mesa do order_id (formato: timestamp_mesa)
                        val tableNumber = try {
                            orderIdStr.split("_").lastOrNull()?.toInt() ?: 1
                        } catch (e: Exception) {
                            1 // Fallback para mesa 1
                        }

                        // Marca o pedido como pago
                        val success = DatabaseManager.markOrderAsPaid(tableNumber)

                        if (success) {
                            android.util.Log.d("CallbackActivity", "✅ Pedido da mesa/comanda $tableNumber marcado como pago")
                        } else {
                            android.util.Log.e("CallbackActivity", "❌ Erro ao marcar pedido como pago")
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.e("CallbackActivity", "Erro ao atualizar status do pedido: ${e.message}")
                }
            }
        }
    }
}

package com.example.guttycielo

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.example.guttycielo.activities.ConnectionActivity
import com.example.guttycielo.activities.MenuActivity
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.ConnectionManager

class MainActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Verifica se já existe uma conexão salva
        val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this)

        if (serverUrl != null && dbConnection != null) {
            // Configura a conexão salva
            NetworkManager.updateBaseUrl(serverUrl)
            DatabaseManager.setCurrentConnection(dbConnection)

            // Vai direto para o menu principal
            val intent = Intent(this, MenuActivity::class.java)
            startActivity(intent)
        } else {
            // Vai para a tela de conexão
            val intent = Intent(this, ConnectionActivity::class.java)
            startActivity(intent)
        }

        finish()
    }
}
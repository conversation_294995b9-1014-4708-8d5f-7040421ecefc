package com.example.guttycielo.models

import java.io.Serializable

data class Product(
    val codigoInterno: Long,
    val codigoGtin: String,
    val descricao: String,
    val descricaoDetalhada: String,
    val grupo: String,
    val categoria: String,
    val precoVenda: Double,
    val qtde: Double
) : Serializable

data class ProductGroup(
    val nome: String,
    val categorias: List<ProductCategory>
)

data class ProductCategory(
    val nome: String,
    val grupo: String,
    val produtos: List<Product>
)

data class CartItem(
    val product: Product,
    var quantidade: Int = 1
) {
    fun getTotalPrice(): Double = product.precoVenda * quantidade
}

data class Order(
    val id: Long? = null,
    val items: List<CartItem>,
    val total: Double,
    val dataHora: String
)

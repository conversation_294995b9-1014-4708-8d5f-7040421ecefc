package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.guttycielo.adapters.ProductCardAdapter
import com.example.guttycielo.databinding.ActivityCategoriesBinding
import com.example.guttycielo.dialogs.QuantityDialog
import com.example.guttycielo.models.Product
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.CartManager
import com.example.guttycielo.utils.ConnectionManager
import kotlinx.coroutines.launch

class ProductsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCategoriesBinding
    private lateinit var productAdapter: ProductCardAdapter
    private var categoria: String = ""

    // Variáveis para modo de edição
    private var isEditMode = false
    private var tableNumber = ""
    private var customerName = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCategoriesBinding.inflate(layoutInflater)
        setContentView(binding.root)

        categoria = intent.getStringExtra("categoria") ?: ""

        // Verifica se está em modo de edição
        isEditMode = intent.getBooleanExtra("edit_mode", false)
        tableNumber = intent.getStringExtra("table_number") ?: ""
        customerName = intent.getStringExtra("customer_name") ?: ""

        setupViews()
        loadProducts()
    }

    private fun setupViews() {
        // Atualiza título baseado no modo
        binding.toolbar.title = if (isEditMode) {
            "Adicionar: $categoria"
        } else {
            categoria
        }

        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        productAdapter = ProductCardAdapter { product ->
            showQuantityDialog(product)
        }

        binding.rvCategories.apply {
            layoutManager = LinearLayoutManager(this@ProductsActivity)
            adapter = productAdapter
        }
    }

    private fun loadProducts() {
        showLoading(true)
        
        lifecycleScope.launch {
            try {
                // Recarrega a conexão
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@ProductsActivity)
                
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)
                } else {
                    Toast.makeText(this@ProductsActivity, "Conexão perdida. Retornando...", Toast.LENGTH_LONG).show()
                    finish()
                    return@launch
                }
                
                val products = DatabaseManager.getProductsByCategory(categoria)
                
                if (products.isNotEmpty()) {
                    productAdapter.submitList(products)
                    showEmptyState(false)
                } else {
                    showEmptyState(true)
                }
            } catch (e: Exception) {
                Toast.makeText(this@ProductsActivity, "Erro ao carregar produtos: ${e.message}", Toast.LENGTH_LONG).show()
                showEmptyState(true)
            } finally {
                showLoading(false)
            }
        }
    }

    private fun showQuantityDialog(product: Product) {
        val dialog = QuantityDialog(this, product) { selectedProduct, quantity ->
            addToCart(selectedProduct, quantity)
        }
        dialog.show()
    }

    private fun addToCart(product: Product, quantity: Int) {
        if (isEditMode) {
            // Modo de edição: retorna o produto selecionado
            val resultIntent = Intent().apply {
                putExtra("selected_product", product)
                putExtra("selected_quantity", quantity)
            }
            setResult(RESULT_OK, resultIntent)
            finish()
        } else {
            // Modo normal: adiciona ao carrinho
            CartManager.addToCart(this, product, quantity)
            showPostAddDialog()
        }
    }

    private fun showPostAddDialog() {
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        builder.setTitle("Produto Adicionado!")
        builder.setMessage("O que deseja fazer agora?")

        builder.setPositiveButton("FINALIZAR PEDIDO") { _, _ ->
            // Navegar para tela de finalização
            val intent = Intent(this, CheckoutActivity::class.java)
            startActivity(intent)
        }

        builder.setNegativeButton("CONTINUAR COMPRANDO") { dialog, _ ->
            dialog.dismiss()
        }

        builder.show()
    }

    private fun showLoading(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
        binding.rvCategories.visibility = if (show) View.GONE else View.VISIBLE
    }

    private fun showEmptyState(show: Boolean) {
        binding.tvEmptyState.visibility = if (show) View.VISIBLE else View.GONE
        binding.rvCategories.visibility = if (show) View.GONE else View.VISIBLE
    }
}

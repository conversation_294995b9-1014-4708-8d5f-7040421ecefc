package com.example.guttycielo.models

import com.google.gson.annotations.SerializedName

/**
 * Modelo para representar uma comanda em aberto
 */
data class ComandaAberta(
    @SerializedName("comanda")
    val comanda: String,
    
    @SerializedName("nome")
    val nome: String,
    
    @SerializedName("total_itens")
    val totalItens: Int,
    
    @SerializedName("valor_total")
    val valorTotal: Double,
    
    @SerializedName("descricao")
    val descricao: String
) {
    /**
     * Retorna a descrição formatada para exibição
     */
    fun getDescricaoFormatada(): String {
        return if (nome.isNotEmpty()) {
            "$comanda - $nome"
        } else {
            comanda
        }
    }
    
    /**
     * Retorna apenas o número da comanda
     */
    fun getNumeroComanda(): String {
        return comanda
    }
    
    /**
     * Retorna apenas o nome (pode ser vazio)
     */
    fun getNomeCliente(): String {
        return nome
    }
}

package com.example.guttycielo.models

import com.google.gson.annotations.SerializedName

data class Vendedor(
    @SerializedName("codigo")
    val codigo: Int,
    
    @SerializedName("nome")
    val nome: String
)

data class VendedorResponse(
    @SerializedName("success")
    val success: <PERSON><PERSON><PERSON>,
    
    @SerializedName("vendedor")
    val vendedor: Vendedor? = null,
    
    @SerializedName("error")
    val error: String? = null
)

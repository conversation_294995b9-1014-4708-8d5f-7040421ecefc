<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Número da pessoa -->
        <TextView
            android:id="@+id/tvPersonNumber"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:text="1"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:background="@drawable/bg_category_chip"
            android:backgroundTint="@color/light_blue"
            android:gravity="center"
            android:layout_marginEnd="16dp" />

        <!-- Informações da parcela -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvPersonTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Pessoa 1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tvAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="R$ 25,00"
                android:textSize="14sp"
                android:textColor="@color/gray" />

        </LinearLayout>

        <!-- Status da parcela -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="NÃO PAGO"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@drawable/bg_category_chip"
                android:backgroundTint="@color/gray"
                android:paddingHorizontal="12dp"
                android:paddingVertical="4dp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tvPaymentMethod"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="10sp"
                android:textColor="@color/gray"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Botão editar valor -->
        <ImageView
            android:id="@+id/btnEditValue"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_menu_edit"
            android:layout_marginStart="8dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            app:tint="@color/blue"
            android:visibility="gone" />

        <!-- Botão remover -->
        <ImageView
            android:id="@+id/btnRemove"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_back"
            android:layout_marginStart="4dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            app:tint="@color/gray"
            android:visibility="gone" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

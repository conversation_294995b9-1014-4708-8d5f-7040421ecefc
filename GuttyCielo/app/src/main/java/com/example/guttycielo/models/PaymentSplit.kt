package com.example.guttycielo.models

import com.example.guttycielo.network.DatabaseManager

/**
 * Representa uma divisão de pagamento
 */
data class PaymentSplit(
    val comandaId: Int,
    var totalParcelas: Int,
    val valorTotal: Double,
    var parcelasPageas: Int = 0,
    var valorPago: Double = 0.0,
    var status: SplitStatus = SplitStatus.EM_ANDAMENTO,
    val parcelas: MutableList<PaymentSplitItem> = mutableListOf()
) {
    
    /**
     * Calcula o valor restante a ser pago (dinamicamente baseado nas parcelas pagas)
     * Trata valores negativos (arredondamento) retornando 0.0
     */
    fun getValorRestante(): Double {
        val valorJaPago = parcelas
            .filter { it.status == PaymentStatus.PAGO || it.status == PaymentStatus.DINHEIRO }
            .sumOf { it.valor }
        val valorRestante = valorTotal - valorJaPago

        // Trata valores negativos causados por arredondamento
        return if (valorRestante < 0.0) {
            println("⚠️ VALOR NEGATIVO DETECTADO: $valorRestante → Arredondando para 0.0")
            0.0
        } else {
            valorRestante
        }
    }
    
    /**
     * Verifica se todas as parcelas foram pagas
     */
    fun isCompleta(): Boolean = parcelasPageas >= totalParcelas
    
    /**
     * Recalcula os valores das parcelas não pagas
     */
    fun recalcularParcelas() {
        val parcelasNaoPagas = parcelas.filter { it.status == PaymentStatus.NAO_PAGO }
        val parcelasPagas = parcelas.filter { it.status == PaymentStatus.PAGO || it.status == PaymentStatus.DINHEIRO }

        println("🔄 RECALCULANDO PARCELAS:")
        println("   - Valor total da comanda: $valorTotal")
        println("   - Parcelas pagas: ${parcelasPagas.size}")
        parcelasPagas.forEach { println("     • Parcela ${it.parcelaNumero}: ${it.valor} [${it.status}]") }
        println("   - Parcelas não pagas: ${parcelasNaoPagas.size}")

        if (parcelasNaoPagas.isNotEmpty()) {
            val valorRestante = getValorRestante()

            // Se valor restante é 0 (por arredondamento), zera todas as parcelas não pagas
            if (valorRestante <= 0.0) {
                println("   - ⚠️ Valor restante é $valorRestante → Zerando parcelas não pagas")
                parcelasNaoPagas.forEach { parcela ->
                    println("   - Zerando parcela ${parcela.parcelaNumero}: ${parcela.valor} → 0.0")
                    parcela.valor = 0.0
                }
            } else {
                val valorPorParcela = valorRestante / parcelasNaoPagas.size

                println("   - Valor já pago: ${valorTotal - valorRestante}")
                println("   - Valor restante: $valorRestante")
                println("   - Novo valor por parcela: $valorPorParcela")

                parcelasNaoPagas.forEach { parcela ->
                    println("   - Atualizando parcela ${parcela.parcelaNumero}: ${parcela.valor} → $valorPorParcela")
                    parcela.valor = valorPorParcela
                }
            }
        }
    }
}

/**
 * Representa um item individual da divisão de pagamento
 */
data class PaymentSplitItem(
    val parcelaNumero: Int,
    var valor: Double,
    var status: PaymentStatus = PaymentStatus.NAO_PAGO,
    var formaPagamento: PaymentMethod? = null,
    var dataPagamento: String? = null,
    var cieloResponse: String? = null
) {
    
    /**
     * Marca a parcela como paga
     */
    fun marcarComoPaga(formaPagamento: PaymentMethod, cieloResponse: String? = null) {
        this.status = PaymentStatus.PAGO
        this.formaPagamento = formaPagamento
        this.cieloResponse = cieloResponse
        this.dataPagamento = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())
    }
    
    /**
     * Marca a parcela como cancelada/recusada
     */
    fun marcarComoFalha(status: PaymentStatus) {
        this.status = status
        this.dataPagamento = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())
    }
}

/**
 * Status possíveis para uma parcela de pagamento
 */
enum class PaymentStatus(val displayName: String, val color: String) {
    NAO_PAGO("NÃO PAGO", "#9E9E9E"),
    PAGO("PAGO", "#4CAF50"),
    CANCELADO("CANCELADO", "#FF9800"),
    RECUSADO("RECUSADO", "#F44336"),
    DINHEIRO("DINHEIRO", "#2196F3")
}

/**
 * Status da divisão de pagamento como um todo
 */
enum class SplitStatus {
    EM_ANDAMENTO,
    CONCLUIDA,
    CANCELADA
}

/**
 * Formas de pagamento disponíveis
 */
enum class PaymentMethod(val displayName: String, val code: String, val statusCode: Int) {
    DEBITO("Débito", "DEBITO_AVISTA", 3),
    CREDITO("Crédito", "CREDITO_AVISTA", 4),
    PIX("PIX", "PIX", 5),
    DINHEIRO("Dinheiro", "DINHEIRO", 6)
}

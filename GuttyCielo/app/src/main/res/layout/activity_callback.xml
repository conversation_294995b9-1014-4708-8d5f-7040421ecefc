<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/light_blue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="Resultado do Pagamento"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back" />

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:gravity="center">

            <!-- Ícone de Status -->
            <ImageView
                android:id="@+id/ivStatusIcon"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="24dp"
                android:src="@drawable/circle_info_solid"
                app:tint="@color/light_blue" />

            <!-- Título do Status -->
            <TextView
                android:id="@+id/tvStatusTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Processando Pagamento..."
                android:textAlignment="center"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <!-- Detalhes do Status -->
            <TextView
                android:id="@+id/tvStatusDetails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Aguarde o resultado do pagamento..."
                android:textAlignment="center"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:layout_marginBottom="32dp" />

            <!-- Card com Informações do Pedido -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:strokeColor="@color/light_blue"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Informações do Pedido"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="ID do Pedido:"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvOrderId"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="#12345" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Valor:"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="R$ 25,50" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layoutReason"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Motivo:"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvReason"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="Cartão recusado" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Botões de Ação -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnTryAgain"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="TENTAR NOVAMENTE"
                    android:backgroundTint="@color/light_blue"
                    android:textSize="16sp"
                    android:visibility="gone"
                    app:cornerRadius="8dp"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnBackToMenu"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="VOLTAR AO MENU"
                    android:backgroundTint="@color/light_blue"
                    android:textSize="16sp"
                    app:cornerRadius="8dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>

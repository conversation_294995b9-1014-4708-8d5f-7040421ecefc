<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <!-- Quantidade -->
    <TextView
        android:id="@+id/tvQuantity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="2x"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/light_blue"
        android:minWidth="40dp"
        android:gravity="center"
        android:layout_marginEnd="12dp" />

    <!-- Informações do produto -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginEnd="12dp">

        <TextView
            android:id="@+id/tvProductName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Coca-Cola 350ml"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tvProductCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Código: 7894900011517"
            android:textSize="12sp"
            android:textColor="@color/gray" />

    </LinearLayout>

    <!-- Preços -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="end">

        <TextView
            android:id="@+id/tvUnitPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="R$ 5,00"
            android:textSize="12sp"
            android:textColor="@color/gray"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tvTotalPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="R$ 10,00"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/light_blue" />

    </LinearLayout>

</LinearLayout>

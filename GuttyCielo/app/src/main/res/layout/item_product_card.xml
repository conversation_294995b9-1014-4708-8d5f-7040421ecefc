<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="6dp"
    app:strokeWidth="1dp"
    app:strokeColor="#E3F2FD"
    android:backgroundTint="#FAFAFA">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/tvProductName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:textColor="#1565C0"
            android:textSize="18sp"
            android:textStyle="bold"
            android:letterSpacing="0.02"
            app:layout_constraintEnd_toStartOf="@+id/btnAddToCart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="COCA-COLA 200ML" />

        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="12dp"
            android:textColor="#546E7A"
            android:textSize="14sp"
            android:alpha="0.8"
            app:layout_constraintEnd_toStartOf="@+id/btnAddToCart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvProductName"
            tools:text="Refrigerante de cola 200ml" />

        <TextView
            android:id="@+id/tvPrice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="12dp"
            android:textColor="#FF8F00"
            android:textSize="22sp"
            android:textStyle="bold"
            android:letterSpacing="0.01"
            app:layout_constraintEnd_toStartOf="@+id/btnAddToCart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvDescription"
            tools:text="R$ 3,50" />

        <TextView
            android:id="@+id/tvStock"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="12dp"
            android:textColor="#4CAF50"
            android:textSize="12sp"
            android:textStyle="bold"
            android:alpha="0.9"
            app:layout_constraintEnd_toStartOf="@+id/btnAddToCart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvPrice"
            tools:text="Estoque: 50" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnAddToCart"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:text="ADICIONAR"
            android:textSize="12sp"
            android:textStyle="bold"
            android:letterSpacing="0.02"
            android:backgroundTint="#42A5F5"
            android:textColor="#FFFFFF"
            app:cornerRadius="24dp"
            app:elevation="4dp"
            app:rippleColor="#BBDEFB"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>

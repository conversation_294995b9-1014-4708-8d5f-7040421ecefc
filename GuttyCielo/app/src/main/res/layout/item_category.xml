<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="32dp"
    android:paddingVertical="12dp">

    <ImageView
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/ic_category"
        app:tint="?attr/colorSecondary" />

    <TextView
        android:id="@+id/tvCategoryName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="Grupo" />

    <ImageView
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/ic_arrow_forward"
        app:tint="@color/black" />

</LinearLayout>

package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.guttycielo.databinding.ActivityConnectionBinding
import com.example.guttycielo.models.DatabaseConnection
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.ConnectionManager
import kotlinx.coroutines.launch

class ConnectionActivity : AppCompatActivity() {

    private lateinit var binding: ActivityConnectionBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityConnectionBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
    }

    private fun setupViews() {
        binding.btnConnect.setOnClickListener {
            val serverUrl = binding.etServerUrl.text.toString().trim()
            val host = binding.etHost.text.toString().trim()
            val port = binding.etPort.text.toString().trim()
            val database = binding.etDatabase.text.toString().trim()
            val username = binding.etUsername.text.toString().trim()
            val password = binding.etPassword.text.toString().trim()

            if (serverUrl.isNotEmpty() && host.isNotEmpty() && port.isNotEmpty() &&
                database.isNotEmpty() && username.isNotEmpty() && password.isNotEmpty()) {

                // Atualiza a URL base do NetworkManager
                NetworkManager.updateBaseUrl(serverUrl)

                val dbConnection = DatabaseConnection(host, port, database, username, password)
                connectToDatabase(serverUrl, dbConnection)
            } else {
                Toast.makeText(this, "Por favor, preencha todos os campos", Toast.LENGTH_SHORT).show()
            }
        }



        binding.btnShowLogs.setOnClickListener {
            val intent = Intent(this, LogsActivity::class.java)
            startActivity(intent)
        }
    }

    private fun connectToDatabase(serverUrl: String, dbConnection: DatabaseConnection) {
        showLoading(true)
        binding.tvConnectionStatus.text = "Conectando ao PostgreSQL..."

        lifecycleScope.launch {
            try {
                // Log inicial para debug
                android.util.Log.d("ConnectionActivity", "Iniciando conexão...")

                val (isConnected, message) = DatabaseManager.testConnection(dbConnection)

                if (isConnected) {
                    binding.tvConnectionStatus.text = message
                    Toast.makeText(this@ConnectionActivity, message, Toast.LENGTH_SHORT).show()

                    // Salva a conexão
                    ConnectionManager.saveConnection(this@ConnectionActivity, serverUrl, dbConnection)

                    // Aguarda um pouco para mostrar a mensagem de sucesso
                    kotlinx.coroutines.delay(1500)

                    // Navega para o login do vendedor
                    val intent = Intent(this@ConnectionActivity, VendedorLoginActivity::class.java)
                    startActivity(intent)
                    finish()
                } else {
                    binding.tvConnectionStatus.text = "Falha: $message"
                    Toast.makeText(this@ConnectionActivity, "Erro: $message", Toast.LENGTH_LONG).show()
                    android.util.Log.e("ConnectionActivity", "Falha na conexão: $message")
                }
            } catch (e: OutOfMemoryError) {
                val errorMsg = "Erro de memória: ${e.message}"
                binding.tvConnectionStatus.text = errorMsg
                Toast.makeText(this@ConnectionActivity, errorMsg, Toast.LENGTH_LONG).show()
                android.util.Log.e("ConnectionActivity", "OutOfMemoryError", e)
            } catch (e: SecurityException) {
                val errorMsg = "Erro de permissão: ${e.message}"
                binding.tvConnectionStatus.text = errorMsg
                Toast.makeText(this@ConnectionActivity, errorMsg, Toast.LENGTH_LONG).show()
                android.util.Log.e("ConnectionActivity", "SecurityException", e)
            } catch (e: ClassNotFoundException) {
                val errorMsg = "Driver não encontrado: ${e.message}"
                binding.tvConnectionStatus.text = errorMsg
                Toast.makeText(this@ConnectionActivity, errorMsg, Toast.LENGTH_LONG).show()
                android.util.Log.e("ConnectionActivity", "ClassNotFoundException", e)
            } catch (e: NoClassDefFoundError) {
                val errorMsg = "Classe não encontrada: ${e.message}"
                binding.tvConnectionStatus.text = errorMsg
                Toast.makeText(this@ConnectionActivity, errorMsg, Toast.LENGTH_LONG).show()
                android.util.Log.e("ConnectionActivity", "NoClassDefFoundError", e)
            } catch (e: Exception) {
                val errorMsg = "Erro inesperado: ${e.message}"
                binding.tvConnectionStatus.text = errorMsg
                Toast.makeText(this@ConnectionActivity, errorMsg, Toast.LENGTH_LONG).show()
                android.util.Log.e("ConnectionActivity", "Exception geral", e)
            } catch (e: Throwable) {
                val errorMsg = "Erro crítico: ${e.message}"
                binding.tvConnectionStatus.text = errorMsg
                Toast.makeText(this@ConnectionActivity, errorMsg, Toast.LENGTH_LONG).show()
                android.util.Log.e("ConnectionActivity", "Throwable", e)
            } finally {
                showLoading(false)
            }
        }
    }



    private fun showLoading(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
        binding.btnConnect.isEnabled = !show
        binding.btnConnect.text = if (show) "" else "Conectar ao Banco PostgreSQL"
    }
}

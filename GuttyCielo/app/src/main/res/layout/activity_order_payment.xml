<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/light_blue"
        app:title="Pagamento"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white" />

    <!-- Indicador de carregamento -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="100dp"
        android:visibility="visible" />

    <!-- <PERSON><PERSON><PERSON><PERSON> principal -->
    <ScrollView
        android:id="@+id/contentLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Informações do pedido -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📋 Detalhes do Pedido"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvCustomerName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="👤 João Silva"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:id="@+id/tvTableNumber"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🏷️ Mesa/Comanda: 8"
                                android:textSize="14sp"
                                android:textColor="@color/gray" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="end">

                            <TextView
                                android:id="@+id/tvTotalItems"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📦 10 itens"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:id="@+id/tvTotalAmount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="💰 R$ 125,50"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/light_blue" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Lista de itens -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🛒 Itens do Pedido"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="12dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvOrderItems"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Seleção de forma de pagamento -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardPaymentMethod"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="💳 Forma de Pagamento"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="12dp" />

                    <RadioGroup
                        android:id="@+id/rgPaymentMethod"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <RadioButton
                            android:id="@+id/rbDebit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="💳 Débito"
                            android:textSize="14sp"
                            android:checked="true"
                            android:layout_marginBottom="8dp" />

                        <RadioButton
                            android:id="@+id/rbCredit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="💳 Crédito"
                            android:textSize="14sp"
                            android:layout_marginBottom="8dp" />

                        <RadioButton
                            android:id="@+id/rbPix"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="📱 PIX"
                            android:textSize="14sp"
                            android:layout_marginBottom="8dp" />

                        <RadioButton
                            android:id="@+id/rbCash"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="💵 Dinheiro"
                            android:textSize="14sp" />

                    </RadioGroup>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Botões de ação -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnProcessPayment"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:text="💳 PAGAR COM CIELO LIO"
                    android:textSize="16sp"
                    android:backgroundTint="@color/light_blue"
                    app:cornerRadius="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSplitPayment"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:text="🔄 DIVIDIR PAGAMENTO"
                    android:textSize="16sp"
                    android:backgroundTint="@color/light_yellow"
                    android:textColor="@color/black"
                    app:cornerRadius="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">



                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnCancel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="❌ CANCELAR"
                        android:textSize="14sp"
                        android:backgroundTint="@color/gray"
                        app:cornerRadius="8dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>

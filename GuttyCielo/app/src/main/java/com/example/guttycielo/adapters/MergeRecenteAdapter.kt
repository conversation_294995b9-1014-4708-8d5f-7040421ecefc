package com.example.guttycielo.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.guttycielo.databinding.ItemMergeRecenteBinding
import com.example.guttycielo.models.MergeLog

/**
 * Adapter para exibir merges recentes com opção de desfazer
 */
class MergeRecenteAdapter(
    private val merges: List<MergeLog>,
    private val onDesfazerClick: (MergeLog) -> Unit
) : RecyclerView.Adapter<MergeRecenteAdapter.MergeRecenteViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MergeRecenteViewHolder {
        val binding = ItemMergeRecenteBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MergeRecenteViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MergeRec<PERSON>ViewHolder, position: Int) {
        holder.bind(merges[position])
    }

    override fun getItemCount(): Int = merges.size

    inner class MergeRecenteViewHolder(
        private val binding: ItemMergeRecenteBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(merge: MergeLog) {
            binding.apply {
                // Status do merge
                if (merge.podeDesfazer()) {
                    tvMergeStatus.text = "ATIVO"
                    tvMergeStatus.setBackgroundColor(android.graphics.Color.parseColor("#4CAF50")) // Verde
                } else {
                    tvMergeStatus.text = "DESFEITO"
                    tvMergeStatus.setBackgroundColor(android.graphics.Color.parseColor("#9E9E9E")) // Cinza
                }

                // Data e hora
                tvMergeDateTime.text = merge.getDataHoraFormatada()

                // Informação principal do merge
                tvMergeInfo.text = merge.getDescricao()

                // Detalhes do merge
                val itensCount = merge.getNumeroItensMovidos()
                val itensText = if (itensCount == 1) "item movido" else "itens movidos"
                val usuario = merge.usuario ?: "Sistema"
                tvMergeDetails.text = "$itensCount $itensText • Usuário: $usuario"

                // Botão desfazer
                if (merge.podeDesfazer()) {
                    btnDesfazer.isEnabled = true
                    btnDesfazer.alpha = 1.0f
                    btnDesfazer.text = "DESFAZER MERGE"
                    btnDesfazer.setBackgroundColor(android.graphics.Color.parseColor("#F44336")) // Vermelho
                    btnDesfazer.setOnClickListener {
                        onDesfazerClick(merge)
                    }
                } else {
                    btnDesfazer.isEnabled = false
                    btnDesfazer.alpha = 0.5f
                    btnDesfazer.text = "JÁ FOI DESFEITO"
                    btnDesfazer.setBackgroundColor(android.graphics.Color.parseColor("#9E9E9E")) // Cinza
                    btnDesfazer.setOnClickListener(null)
                }
            }
        }
    }
}

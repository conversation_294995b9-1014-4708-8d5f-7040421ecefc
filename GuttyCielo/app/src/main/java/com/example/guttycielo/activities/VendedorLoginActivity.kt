package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.guttycielo.databinding.ActivityVendedorLoginBinding
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.utils.SessionManager
import kotlinx.coroutines.launch

class VendedorLoginActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityVendedorLoginBinding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityVendedorLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
    }
    
    private fun setupUI() {
        binding.btnLogin.setOnClickListener {
            val codigo = binding.etCodigoVendedor.text.toString().trim()
            
            if (codigo.isEmpty()) {
                Toast.makeText(this, "Informe o código do vendedor", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            loginVendedor(codigo)
        }
        
        // Foco automático no campo de código
        binding.etCodigoVendedor.requestFocus()
    }
    
    private fun loginVendedor(codigo: String) {
        binding.btnLogin.isEnabled = false
        binding.progressBar.visibility = android.view.View.VISIBLE
        
        lifecycleScope.launch {
            try {
                val vendedorResponse = DatabaseManager.getVendedor(codigo)

                // Se chegou aqui, o vendedor foi encontrado e liberado
                if (vendedorResponse != null) {
                    SessionManager.saveVendedor(this@VendedorLoginActivity, vendedorResponse)

                    // Vai para o menu principal
                    val intent = Intent(this@VendedorLoginActivity, MenuActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    startActivity(intent)
                    finish()
                } else {
                    Toast.makeText(this@VendedorLoginActivity, "Erro inesperado: vendedor nulo", Toast.LENGTH_SHORT).show()
                }

            } catch (e: Exception) {
                // Mostra a mensagem específica do erro (do servidor)
                Toast.makeText(this@VendedorLoginActivity, e.message, Toast.LENGTH_LONG).show()
            } finally {
                binding.btnLogin.isEnabled = true
                binding.progressBar.visibility = android.view.View.GONE
            }
        }
    }
}

package com.example.guttycielo.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.guttycielo.databinding.ItemCategoryCardBinding

class CategoryCardAdapter(
    private val onCategoryClick: (String) -> Unit
) : RecyclerView.Adapter<CategoryCardAdapter.CategoryViewHolder>() {

    private var categories = listOf<String>()

    fun submitList(newCategories: List<String>) {
        categories = newCategories
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        val binding = ItemCategoryCardBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CategoryViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        holder.bind(categories[position])
    }

    override fun getItemCount(): Int = categories.size

    inner class CategoryViewHolder(
        private val binding: ItemCategoryCardBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(category: String) {
            binding.apply {
                tvName.text = category
                
                root.setOnClickListener {
                    onCategoryClick(category)
                }
            }
        }
    }
}

package com.example.guttycielo.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.guttycielo.databinding.ItemOrderItemBinding
import com.example.guttycielo.models.CartItem
import java.text.NumberFormat
import java.util.Locale

/**
 * Adapter para exibir itens de um pedido
 */
class OrderItemsAdapter(
    private val items: MutableList<CartItem>,
    private val onItemClick: (CartItem) -> Unit
) : RecyclerView.Adapter<OrderItemsAdapter.OrderItemViewHolder>() {

    private val currencyFormatter = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderItemViewHolder {
        val binding = ItemOrderItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return OrderItemViewHolder(binding)
    }

    override fun onBindViewHolder(holder: OrderItemViewHolder, position: Int) {
        holder.bind(items[position])
    }

    override fun getItemCount(): Int = items.size

    fun updateItems(newItems: List<CartItem>) {
        items.clear()
        items.addAll(newItems)
        notifyDataSetChanged()
    }

    inner class OrderItemViewHolder(
        private val binding: ItemOrderItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: CartItem) {
            binding.apply {
                // Informações do produto
                tvProductName.text = item.product.descricao
                tvProductCode.text = "Código: ${item.product.codigoGtin.ifEmpty { item.product.codigoInterno.toString() }}"
                
                // Quantidade e preços
                tvQuantity.text = "${item.quantidade}x"
                tvUnitPrice.text = currencyFormatter.format(item.product.precoVenda)
                
                val totalPrice = item.product.precoVenda * item.quantidade
                tvTotalPrice.text = currencyFormatter.format(totalPrice)

                // Click no item
                root.setOnClickListener {
                    onItemClick(item)
                }
            }
        }
    }
}

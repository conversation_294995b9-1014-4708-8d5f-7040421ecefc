<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/light_blue"
        app:title="Dividir Pagamento"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Informações do pedido -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📋 Informações do Pedido"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tvOrderInfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Mesa: 5 | Cliente: João Silva\nTotal: R$ 100,00"
                        android:textSize="14sp"
                        android:textColor="@color/gray" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Configuração da divisão -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardSplitConfig"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🔄 Configurar Divisão"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="12dp"
                        />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center"

                        >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Dividir em:"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:layout_marginEnd="12dp" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="12dp"
                            app:boxStrokeColor="@color/light_blue">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etSplitCount"
                                android:layout_width="match_parent"
                                android:layout_height="50dp"
                                android:inputType="number"
                                android:text="2"
                                android:textAlignment="center"
                                android:textSize="16sp" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="pessoas"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            />


                    </LinearLayout>
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnCreateSplit"
                        android:layout_width="290dp"
                        android:layout_marginTop="20dp"
                        android:layout_height="wrap_content"
                        android:text="CRIAR"
                        android:textSize="14sp"
                        android:backgroundTint="@color/light_blue"
                        android:layout_gravity="center"
                        app:cornerRadius="8dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Status da divisão -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardSplitStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📊 Status do Pagamento"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tvSplitProgress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="2 de 4 pagamentos concluídos\nFALTA: R$ 50,00"
                        android:textSize="14sp"
                        android:textColor="@color/gray" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Container para as parcelas -->
            <LinearLayout
                android:id="@+id/containerSplitItems"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" />

        </LinearLayout>

    </ScrollView>

    <!-- Botões de ação -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/white"
        android:elevation="8dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnCancel"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="CANCELAR"
            android:textSize="16sp"
            android:backgroundTint="@color/gray"
            app:cornerRadius="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnFinish"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="FINALIZAR"
            android:textSize="16sp"
            android:backgroundTint="@color/light_blue"
            android:enabled="false"
            app:cornerRadius="8dp" />

    </LinearLayout>

</LinearLayout>

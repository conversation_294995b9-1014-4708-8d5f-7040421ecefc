package com.example.guttycielo.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.guttycielo.databinding.ItemPendingOrderBinding
import com.example.guttycielo.models.PendingOrder
import java.text.NumberFormat
import java.util.Locale

/**
 * Adapter para exibir lista de pedidos em aberto
 */
class PendingOrdersAdapter(
    private val orders: List<PendingOrder>,
    private val onOrderClick: (PendingOrder) -> Unit,
    private val onEditClick: (PendingOrder) -> Unit,
    private val onPrintClick: ((PendingOrder) -> Unit)? = null,
    private val isViewOnlyMode: Boolean = false
) : RecyclerView.Adapter<PendingOrdersAdapter.PendingOrderViewHolder>() {

    private val currencyFormatter = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PendingOrderViewHolder {
        val binding = ItemPendingOrderBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PendingOrderViewHolder(binding)
    }

    override fun onBindViewHolder(holder: PendingOrderViewHolder, position: Int) {
        holder.bind(orders[position])
    }

    override fun getItemCount(): Int = orders.size

    inner class PendingOrderViewHolder(
        private val binding: ItemPendingOrderBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(order: PendingOrder) {
            binding.apply {
                // Informações principais
                tvCustomerName.text = if (order.customerName.isNotEmpty()) {
                    "Cliente: ${order.customerName}"
                } else {
                    "Cliente não informado"
                }

                tvTableNumber.text = "Mesa/Comanda: ${order.tableNumber}"
                tvTotalItems.text = "${order.totalItems} ${if (order.totalItems == 1) "item" else "itens"}"
                tvTotalAmount.text = currencyFormatter.format(order.totalAmount)

                // Lista resumida dos itens (primeiros 3)
                val itemsText = order.items.take(3).joinToString("\n") { item ->
                    "• ${item.quantity}x ${item.productName} - ${currencyFormatter.format(item.totalPrice)}"
                }
                
                val moreItemsText = if (order.items.size > 3) {
                    "\n... e mais ${order.items.size - 3} ${if (order.items.size - 3 == 1) "item" else "itens"}"
                } else {
                    ""
                }
                
                tvItemsList.text = itemsText + moreItemsText

                // Botões de ação baseados no modo
                if (isViewOnlyMode) {
                    // Modo visualização: esconde botão PAGAR
                    btnPayOrder.visibility = android.view.View.GONE
                } else {
                    // Modo pagamento: mostra botão PAGAR
                    btnPayOrder.visibility = android.view.View.VISIBLE
                    btnPayOrder.setOnClickListener {
                        onOrderClick(order)
                    }
                }

                btnEditOrder.setOnClickListener {
                    onEditClick(order)
                }

                // Botão de impressão (só no modo visualização)
                if (isViewOnlyMode) {
                    btnPrintOrder.visibility = View.VISIBLE
                    btnPrintOrder.setOnClickListener {
                        onPrintClick?.invoke(order)
                    }
                } else {
                    btnPrintOrder.visibility = View.GONE
                }

                // Click no card inteiro
                root.setOnClickListener {
                    onOrderClick(order)
                }
            }
        }
    }
}

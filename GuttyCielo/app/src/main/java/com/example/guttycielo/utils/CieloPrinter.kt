package com.example.guttycielo.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Base64
import android.util.Log
import com.example.guttycielo.models.PendingOrder
import com.google.gson.Gson
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

/**
 * Classe para impressão via Cielo LIO usando Deep Link
 */
class CieloPrinter(private val context: Context) {
    
    companion object {
        private const val TAG = "CieloPrinter"
        private const val PRINT_DEEP_LINK = "lio://print"
        private const val CALLBACK_URL = "order://response"
    }
    
    private val currencyFormatter = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
    private val dateFormatter = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("pt", "BR"))
    
    /**
     * Imprime um pedido completo
     */
    fun printOrder(order: PendingOrder) {
        try {
            Log.d(TAG, "🖨️ Iniciando impressão do pedido:")
            Log.d(TAG, "   - Cliente: ${order.customerName}")
            Log.d(TAG, "   - Mesa: ${order.tableNumber}")
            Log.d(TAG, "   - Itens: ${order.items.size}")

            // Salva informação de que é impressão (não pagamento)
            val sharedPrefs = context.getSharedPreferences("print_callback", Context.MODE_PRIVATE)
            sharedPrefs.edit()
                .putBoolean("is_printing", true)
                .putString("print_customer", order.customerName)
                .putString("print_table", order.tableNumber)
                .apply()

            val printJson = createPrintJson(order)
            val base64Json = Base64.encodeToString(printJson.toByteArray(), Base64.NO_WRAP)

            Log.d(TAG, "📄 JSON de impressão criado:")
            Log.d(TAG, printJson)

            val deepLinkUri = Uri.parse("$PRINT_DEEP_LINK?request=$base64Json&urlCallback=$CALLBACK_URL")

            val intent = Intent(Intent.ACTION_VIEW, deepLinkUri).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            Log.d(TAG, "🚀 Enviando para impressão via deep link")
            context.startActivity(intent)

        } catch (e: Exception) {
            Log.e(TAG, "❌ Erro ao imprimir pedido: ${e.message}")
            throw e
        }
    }
    
    /**
     * Cria o JSON para impressão do pedido
     */
    private fun createPrintJson(order: PendingOrder): String {
        val printData = PrintRequest(
            operation = "PRINT_TEXT",
            styles = listOf(
                PrintStyle(
                    key_attributes_align = 1, // Left align
                    key_attributes_textsize = 30, // Tamanho médio
                    key_attributes_typeface = 1, // Negrito
                    key_attributes_linespace = 5, // Espaçamento entre linhas
                    key_attributes_marginbottom = 50, // Margem inferior grande
                    form_feed = 1 // Espaçamento após impressão para LIO
                )
            ),
            value = listOf(createPrintContent(order))
        )

        return Gson().toJson(printData)
    }
    
    /**
     * Cria o conteúdo de texto para impressão
     */
    private fun createPrintContent(order: PendingOrder): String {
        val content = StringBuilder()

        // Informações da comanda
        content.append("MESA/COMANDA: ${order.tableNumber}\n")
        content.append("CLIENTE: ${order.customerName}\n")
        content.append("DATA: ${dateFormatter.format(Date())}\n\n")

        // Lista de itens (apenas quantidade e produto)
        order.items.forEach { item ->
            content.append("${item.quantity}x ${item.productName}\n")
        }

        // O espaçamento será controlado pelos atributos da Cielo (marginbottom + form_feed)
        return content.toString()
    }
    
    /**
     * Classes para serialização JSON
     */
    data class PrintRequest(
        val operation: String,
        val styles: List<PrintStyle>,
        val value: List<String>
    )
    
    data class PrintStyle(
        val key_attributes_align: Int = 1, // 0=Center, 1=Left, 2=Right
        val key_attributes_textsize: Int = 30,
        val key_attributes_typeface: Int = 0, // 0-8 diferentes fontes
        val key_attributes_marginleft: Int? = null, // Margem esquerda
        val key_attributes_marginright: Int? = null, // Margem direita
        val key_attributes_margintop: Int? = null, // Margem superior
        val key_attributes_marginbottom: Int? = null, // Margem inferior
        val key_attributes_linespace: Int? = null, // Espaçamento entre linhas
        val key_attributes_weight: Int? = null, // Peso da coluna
        val form_feed: Int? = null // 0=sem espaçamento, 1=com espaçamento
    )
}

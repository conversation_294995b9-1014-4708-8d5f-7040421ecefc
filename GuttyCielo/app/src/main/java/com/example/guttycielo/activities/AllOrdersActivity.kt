package com.example.guttycielo.activities

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.guttycielo.adapters.OrdersAdapter
import com.example.guttycielo.databinding.ActivityAllOrdersBinding
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.ConnectionManager
import kotlinx.coroutines.launch

class AllOrdersActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAllOrdersBinding
    private lateinit var ordersAdapter: OrdersAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAllOrdersBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        loadOrders()
    }

    private fun setupViews() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        ordersAdapter = OrdersAdapter()

        binding.rvOrders.apply {
            layoutManager = LinearLayoutManager(this@AllOrdersActivity)
            adapter = ordersAdapter
        }
    }

    private fun loadOrders() {
        showLoading(true)
        
        lifecycleScope.launch {
            try {
                // Recarrega a conexão
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@AllOrdersActivity)
                
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)
                    
                    val orders = DatabaseManager.getAllOrders()

                    if (orders.isNotEmpty()) {
                        ordersAdapter.submitList(orders)
                        showEmptyState(false)
                    } else {
                        showEmptyState(true)
                    }
                } else {
                    Toast.makeText(this@AllOrdersActivity, "Conexão perdida. Retornando...", Toast.LENGTH_LONG).show()
                    finish()
                }
            } catch (e: Exception) {
                Toast.makeText(this@AllOrdersActivity, "Erro ao carregar pedidos: ${e.message}", Toast.LENGTH_LONG).show()
                showEmptyState(true)
            } finally {
                showLoading(false)
            }
        }
    }

    private fun showLoading(show: Boolean) {
        binding.progressBar.visibility = if (show) View.VISIBLE else View.GONE
        binding.rvOrders.visibility = if (show) View.GONE else View.VISIBLE
    }

    private fun showEmptyState(show: Boolean) {
        binding.tvEmptyState.visibility = if (show) View.VISIBLE else View.GONE
        binding.rvOrders.visibility = if (show) View.GONE else View.VISIBLE
    }
}

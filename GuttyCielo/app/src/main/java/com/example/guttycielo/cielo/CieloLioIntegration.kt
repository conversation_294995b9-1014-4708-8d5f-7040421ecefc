package com.example.guttycielo.cielo

import android.app.Activity
import android.content.Intent
import android.util.Log
import com.example.guttycielo.config.CieloLioConfig
import com.example.guttycielo.models.CartItem
import java.text.NumberFormat
import java.util.Locale

/**
 * Integração com Cielo LIO usando Intents para funcionar com o LIO EMULATOR
 * 
 * Esta implementação funciona com o LIO EMULATOR oficial da Cielo
 * Download: https://s3-sa-east-1.amazonaws.com/cielo-lio-store/apps/lio-emulator/1.60.3/lio-emulator.apk
 */
class CieloLioIntegration(private val activity: Activity) {

    companion object {
        const val REQUEST_CODE_PAYMENT = 1001
        const val CIELO_LIO_PACKAGE = "cielo.launcher"
        const val CIELO_EMULATOR_PACKAGE = "cielo.lio.emulator"
        
        // Actions para comunicação com a LIO
        const val ACTION_PAYMENT = "cielo.intent.action.PAYMENT"
        const val ACTION_PAYMENT_V2 = "cielo.intent.action.PAYMENT_V2"
        
        // Extras para enviar dados
        const val EXTRA_ORDER_ID = "EXTRA_ORDER_ID"
        const val EXTRA_AMOUNT = "EXTRA_AMOUNT"
        const val EXTRA_PAYMENT_CODE = "EXTRA_PAYMENT_CODE"
        const val EXTRA_INSTALLMENTS = "EXTRA_INSTALLMENTS"
        const val EXTRA_EMAIL = "EXTRA_EMAIL"
        const val EXTRA_CLIENT_ID = "EXTRA_CLIENT_ID"
        const val EXTRA_ACCESS_TOKEN = "EXTRA_ACCESS_TOKEN"
        
        // Extras de retorno
        const val EXTRA_RESULT = "EXTRA_RESULT"
        const val EXTRA_PAYMENT_STATUS = "EXTRA_PAYMENT_STATUS"
        const val EXTRA_AUTHORIZATION_CODE = "EXTRA_AUTHORIZATION_CODE"
        const val EXTRA_CIELO_CODE = "EXTRA_CIELO_CODE"
        const val EXTRA_BRAND = "EXTRA_BRAND"
        const val EXTRA_MASK = "EXTRA_MASK"
        
        // Status de pagamento
        const val PAYMENT_APPROVED = "APPROVED"
        const val PAYMENT_DECLINED = "DECLINED"
        const val PAYMENT_CANCELLED = "CANCELLED"
    }

    /**
     * Inicia o processo de pagamento na Cielo LIO
     */
    fun startPayment(
        cartItems: List<CartItem>,
        orderReference: String,
        customerName: String? = null,
        tableNumber: Int = 1,
        observation: String? = null,
        paymentListener: PaymentListener
    ) {
        if (!CieloLioConfig.isConfigured()) {
            paymentListener.onError("Credenciais da Cielo LIO não configuradas")
            return
        }

        try {
            // Calcula o valor total em centavos
            val totalInReais = cartItems.sumOf { it.product.precoVenda * it.quantidade }
            val totalInCents = (totalInReais * 100).toLong()

            Log.d("CieloLioIntegration", "=== INICIANDO PAGAMENTO CIELO LIO ===")
            Log.d("CieloLioIntegration", "Referência: $orderReference")
            Log.d("CieloLioIntegration", "Valor total: ${formatCurrency(totalInReais)}")
            Log.d("CieloLioIntegration", "Valor em centavos: $totalInCents")
            Log.d("CieloLioIntegration", "Cliente: $customerName")
            Log.d("CieloLioIntegration", "Mesa: $tableNumber")

            // Cria o Intent para a Cielo LIO
            val intent = Intent().apply {
                // Tenta primeiro o emulador, depois a LIO real
                setClassName(CIELO_EMULATOR_PACKAGE, "cielo.lio.emulator.MainActivity")
                action = ACTION_PAYMENT_V2
                
                // Dados do pagamento
                putExtra(EXTRA_ORDER_ID, orderReference)
                putExtra(EXTRA_AMOUNT, totalInCents)
                putExtra(EXTRA_CLIENT_ID, CieloLioConfig.CLIENT_ID)
                putExtra(EXTRA_ACCESS_TOKEN, CieloLioConfig.ACCESS_TOKEN)
                
                // Dados opcionais
                customerName?.let { putExtra("EXTRA_CUSTOMER_NAME", it) }
                putExtra("EXTRA_TABLE_NUMBER", tableNumber)
                observation?.let { putExtra("EXTRA_OBSERVATION", it) }
                
                // Adiciona itens do carrinho como JSON
                val itemsJson = cartItems.map { item ->
                    mapOf(
                        "sku" to item.product.codigoInterno.toString(),
                        "name" to item.product.descricao,
                        "unitPrice" to (item.product.precoVenda * 100).toInt(),
                        "quantity" to item.quantidade,
                        "unitOfMeasure" to "UNIDADE"
                    )
                }
                putExtra("EXTRA_ITEMS", itemsJson.toString())
            }

            // Verifica se o emulador está instalado
            val packageManager = activity.packageManager
            val resolveInfo = packageManager.resolveActivity(intent, 0)

            if (resolveInfo == null) {
                // Tenta com a LIO real
                intent.setClassName(CIELO_LIO_PACKAGE, "cielo.launcher.MainActivity")
                val resolveInfoReal = packageManager.resolveActivity(intent, 0)
                
                if (resolveInfoReal == null) {
                    paymentListener.onError("Cielo LIO ou LIO EMULATOR não encontrado. Instale o LIO EMULATOR para testes.")
                    return
                }
            }

            // Inicia a activity da Cielo LIO
            activity.startActivityForResult(intent, REQUEST_CODE_PAYMENT)
            paymentListener.onStart()

        } catch (e: Exception) {
            Log.e("CieloLioIntegration", "Erro ao iniciar pagamento", e)
            paymentListener.onError("Erro ao iniciar pagamento: ${e.message}")
        }
    }

    /**
     * Processa o resultado do pagamento retornado pela Cielo LIO
     */
    fun handlePaymentResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
        paymentListener: PaymentListener
    ) {
        if (requestCode != REQUEST_CODE_PAYMENT) {
            return
        }

        Log.d("CieloLioIntegration", "=== RESULTADO DO PAGAMENTO ===")
        Log.d("CieloLioIntegration", "Request Code: $requestCode")
        Log.d("CieloLioIntegration", "Result Code: $resultCode")

        when (resultCode) {
            Activity.RESULT_OK -> {
                // Pagamento aprovado
                val paymentStatus = data?.getStringExtra(EXTRA_PAYMENT_STATUS) ?: PAYMENT_APPROVED
                val authCode = data?.getStringExtra(EXTRA_AUTHORIZATION_CODE) ?: ""
                val cieloCode = data?.getStringExtra(EXTRA_CIELO_CODE) ?: ""
                val brand = data?.getStringExtra(EXTRA_BRAND) ?: ""
                val mask = data?.getStringExtra(EXTRA_MASK) ?: ""

                Log.d("CieloLioIntegration", "✅ Pagamento aprovado")
                Log.d("CieloLioIntegration", "Status: $paymentStatus")
                Log.d("CieloLioIntegration", "Auth Code: $authCode")
                Log.d("CieloLioIntegration", "Cielo Code: $cieloCode")
                Log.d("CieloLioIntegration", "Bandeira: $brand")
                Log.d("CieloLioIntegration", "Máscara: $mask")

                val paymentResult = PaymentResult(
                    status = paymentStatus,
                    authorizationCode = authCode,
                    cieloCode = cieloCode,
                    brand = brand,
                    mask = mask,
                    isApproved = true
                )

                paymentListener.onPaymentSuccess(paymentResult)
            }

            Activity.RESULT_CANCELED -> {
                // Pagamento cancelado
                Log.d("CieloLioIntegration", "❌ Pagamento cancelado")
                paymentListener.onCancel()
            }

            else -> {
                // Erro no pagamento
                val errorMessage = data?.getStringExtra("EXTRA_ERROR_MESSAGE") ?: "Erro desconhecido"
                Log.e("CieloLioIntegration", "❌ Erro no pagamento: $errorMessage")
                paymentListener.onError("Erro no pagamento: $errorMessage")
            }
        }
    }

    private fun formatCurrency(value: Double): String {
        val formatter = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
        return formatter.format(value)
    }
}

/**
 * Interface para callbacks do pagamento
 */
interface PaymentListener {
    fun onStart()
    fun onPaymentSuccess(result: PaymentResult)
    fun onCancel()
    fun onError(message: String)
}

/**
 * Resultado do pagamento
 */
data class PaymentResult(
    val status: String,
    val authorizationCode: String,
    val cieloCode: String,
    val brand: String,
    val mask: String,
    val isApproved: Boolean
)

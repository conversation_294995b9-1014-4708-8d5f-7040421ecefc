package com.example.guttycielo.utils

import android.content.Context
import android.content.SharedPreferences
import com.example.guttycielo.models.DatabaseConnection

object ConnectionManager {
    
    private const val PREFS_NAME = "guttycielo_connection"
    private const val KEY_SERVER_URL = "server_url"
    private const val KEY_HOST = "host"
    private const val KEY_PORT = "port"
    private const val KEY_DATABASE = "database"
    private const val KEY_USERNAME = "username"
    private const val KEY_PASSWORD = "password"
    private const val KEY_IS_CONNECTED = "is_connected"
    
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    fun saveConnection(context: Context, serverUrl: String, dbConnection: DatabaseConnection) {
        val prefs = getPrefs(context)
        prefs.edit().apply {
            putString(KEY_SERVER_URL, serverUrl)
            putString(KEY_HOST, dbConnection.host)
            putString(KEY_PORT, dbConnection.port)
            putString(KEY_DATABASE, dbConnection.database)
            putString(KEY_USERNAME, dbConnection.username)
            putString(KEY_PASSWORD, dbConnection.password)
            putBoolean(KEY_IS_CONNECTED, true)
            apply()
        }
    }
    
    fun getSavedConnection(context: Context): Pair<String?, DatabaseConnection?> {
        val prefs = getPrefs(context)
        
        if (!prefs.getBoolean(KEY_IS_CONNECTED, false)) {
            return Pair(null, null)
        }
        
        val serverUrl = prefs.getString(KEY_SERVER_URL, null)
        val host = prefs.getString(KEY_HOST, null)
        val port = prefs.getString(KEY_PORT, null)
        val database = prefs.getString(KEY_DATABASE, null)
        val username = prefs.getString(KEY_USERNAME, null)
        val password = prefs.getString(KEY_PASSWORD, null)
        
        return if (serverUrl != null && host != null && port != null && 
                   database != null && username != null && password != null) {
            val dbConnection = DatabaseConnection(host, port, database, username, password)
            Pair(serverUrl, dbConnection)
        } else {
            Pair(null, null)
        }
    }
    
    fun clearConnection(context: Context) {
        val prefs = getPrefs(context)
        prefs.edit().clear().apply()
    }
    
    fun isConnected(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_IS_CONNECTED, false)
    }
}

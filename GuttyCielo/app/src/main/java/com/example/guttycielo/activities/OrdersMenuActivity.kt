package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.example.guttycielo.databinding.ActivityOrdersMenuBinding
import com.example.guttycielo.utils.CartManager

class OrdersMenuActivity : AppCompatActivity() {

    private lateinit var binding: ActivityOrdersMenuBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOrdersMenuBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
    }

    private fun setupViews() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }

        binding.cardPendingOrders.setOnClickListener {
            // Verifica se há itens no carrinho
            val cartItems = CartManager.getCartItems(this)
            if (cartItems.isNotEmpty()) {
                // Vai direto para a tela de checkout
                val intent = Intent(this, CheckoutActivity::class.java)
                startActivity(intent)
            } else {
                // Mostra mensagem que não há pedidos pendentes
                androidx.appcompat.app.AlertDialog.Builder(this)
                    .setTitle("Nenhum Pedido Pendente")
                    .setMessage("Não há itens no carrinho atual.")
                    .setPositiveButton("OK") { dialog, _ -> dialog.dismiss() }
                    .show()
            }
        }

        binding.cardAllOrders.setOnClickListener {
            val intent = Intent(this, AllOrdersActivity::class.java)
            startActivity(intent)
        }
    }
}

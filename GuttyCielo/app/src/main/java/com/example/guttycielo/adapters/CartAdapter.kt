package com.example.guttycielo.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.guttycielo.databinding.ItemCartBinding
import com.example.guttycielo.models.CartItem
import java.text.NumberFormat
import java.util.Locale

class CartAdapter(
    private val onQuantityChanged: (CartItem, Int) -> Unit,
    private val onRemoveItem: (CartItem) -> Unit
) : ListAdapter<CartItem, CartAdapter.CartViewHolder>(CartDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CartViewHolder {
        val binding = ItemCartBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CartViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>tViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class CartViewHolder(
        private val binding: ItemCartBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(cartItem: CartItem) {
            binding.apply {
                val currencyFormat = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
                
                tvProductName.text = cartItem.product.descricao
                tvUnitPrice.text = "${currencyFormat.format(cartItem.product.precoVenda)} cada"
                tvQuantity.text = cartItem.quantidade.toString()
                tvTotalPrice.text = "Total: ${currencyFormat.format(cartItem.getTotalPrice())}"
                
                btnMinus.setOnClickListener {
                    if (cartItem.quantidade > 1) {
                        onQuantityChanged(cartItem, cartItem.quantidade - 1)
                    }
                }
                
                btnPlus.setOnClickListener {
                    onQuantityChanged(cartItem, cartItem.quantidade + 1)
                }

                btnRemove.setOnClickListener {
                    onRemoveItem(cartItem)
                }
            }
        }
    }

    class CartDiffCallback : DiffUtil.ItemCallback<CartItem>() {
        override fun areItemsTheSame(oldItem: CartItem, newItem: CartItem): Boolean {
            return oldItem.product.codigoGtin == newItem.product.codigoGtin
        }

        override fun areContentsTheSame(oldItem: CartItem, newItem: CartItem): Boolean {
            return oldItem == newItem
        }
    }
}

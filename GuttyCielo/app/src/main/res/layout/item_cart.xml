<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="04dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:background="@color/blue_clear"
        >

        <TextView
            android:id="@+id/tvProductName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/layoutQuantity"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="COCA-COLA 200ML" />

        <TextView
            android:id="@+id/tvUnitPrice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@+id/layoutQuantity"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvProductName"
            tools:text="R$ 3,50 cada" />

        <LinearLayout
            android:id="@+id/layoutQuantity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnMinus"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:padding="0dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:insetLeft="0dp"
                android:insetRight="0dp"
                android:text="-"
                android:textSize="20sp"
                app:cornerRadius="4dp"
                android:textColor="@color/blue"
                />

            <TextView
                android:id="@+id/tvQuantity"
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="2" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnPlus"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:padding="0dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:insetLeft="0dp"
                android:insetRight="0dp"
                android:text="+"
                android:textSize="15sp"
                app:cornerRadius="4dp"

                android:textColor="@color/blue"
                />

        </LinearLayout>

        <TextView
            android:id="@+id/tvTotalPrice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:gravity="end"
            android:textColor="@color/blue"
            android:textSize="25sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvUnitPrice"
            tools:text="Total: R$ 7,00" />

        <com.google.android.material.button.MaterialButton
            android:textColor="@color/blue"
            android:id="@+id/btnRemove"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:layout_marginEnd="28dp"
            android:text="REMOVER"
            android:textSize="12sp"
            app:cornerRadius="4dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvTotalPrice"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTotalPrice"
            app:layout_constraintVertical_bias="0.512" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>

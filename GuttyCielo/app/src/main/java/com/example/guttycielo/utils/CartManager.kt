package com.example.guttycielo.utils

import android.content.Context
import android.content.SharedPreferences
import com.example.guttycielo.models.CartItem
import com.example.guttycielo.models.Product
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

object CartManager {
    
    private const val PREFS_NAME = "guttycielo_cart"
    private const val KEY_CART_ITEMS = "cart_items"
    
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    fun addToCart(context: Context, product: Product, quantity: Int) {
        val currentItems = getCartItems(context).toMutableList()
        
        // Verifica se o produto já existe no carrinho
        val existingItemIndex = currentItems.indexOfFirst { 
            it.product.codigoGtin == product.codigoGtin 
        }
        
        if (existingItemIndex >= 0) {
            // Atualiza a quantidade do item existente
            currentItems[existingItemIndex].quantidade += quantity
        } else {
            // Adiciona novo item
            currentItems.add(CartItem(product, quantity))
        }
        
        saveCartItems(context, currentItems)
    }
    
    fun removeFromCart(context: Context, codigoGtin: String) {
        val currentItems = getCartItems(context).toMutableList()
        currentItems.removeAll { it.product.codigoGtin == codigoGtin }
        saveCartItems(context, currentItems)
    }
    
    fun updateQuantity(context: Context, codigoGtin: String, newQuantity: Int) {
        val currentItems = getCartItems(context).toMutableList()
        val itemIndex = currentItems.indexOfFirst { 
            it.product.codigoGtin == codigoGtin 
        }
        
        if (itemIndex >= 0) {
            if (newQuantity <= 0) {
                currentItems.removeAt(itemIndex)
            } else {
                currentItems[itemIndex].quantidade = newQuantity
            }
            saveCartItems(context, currentItems)
        }
    }
    
    fun getCartItems(context: Context): List<CartItem> {
        val prefs = getPrefs(context)
        val json = prefs.getString(KEY_CART_ITEMS, null) ?: return emptyList()
        
        return try {
            val type = object : TypeToken<List<CartItem>>() {}.type
            Gson().fromJson(json, type) ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    fun getCartTotal(context: Context): Double {
        return getCartItems(context).sumOf { it.getTotalPrice() }
    }
    
    fun getCartItemCount(context: Context): Int {
        return getCartItems(context).sumOf { it.quantidade }
    }
    
    fun clearCart(context: Context) {
        saveCartItems(context, emptyList())
    }
    
    private fun saveCartItems(context: Context, items: List<CartItem>) {
        val prefs = getPrefs(context)
        val json = Gson().toJson(items)
        prefs.edit().putString(KEY_CART_ITEMS, json).apply()
    }
}

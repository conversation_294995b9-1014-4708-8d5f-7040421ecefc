package com.example.guttycielo.utils

import android.content.Context
import android.content.SharedPreferences
import com.example.guttycielo.models.Vendedor

object SessionManager {
    
    private const val PREF_NAME = "vendedor_session"
    private const val KEY_VENDEDOR_CODIGO = "vendedor_codigo"
    private const val KEY_VENDEDOR_NOME = "vendedor_nome"
    private const val KEY_IS_LOGGED_IN = "is_logged_in"
    
    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }
    
    fun saveVendedor(context: Context, vendedor: Vendedor) {
        val prefs = getPreferences(context)
        prefs.edit().apply {
            putInt(KEY_VENDEDOR_CODIGO, vendedor.codigo)
            putString(KEY_VENDEDOR_NOME, vendedor.nome)
            putBoolean(KEY_IS_LOGGED_IN, true)
            apply()
        }
    }
    
    fun getVendedor(context: Context): Vendedor? {
        val prefs = getPreferences(context)
        
        if (!prefs.getBoolean(KEY_IS_LOGGED_IN, false)) {
            return null
        }
        
        val codigo = prefs.getInt(KEY_VENDEDOR_CODIGO, -1)
        val nome = prefs.getString(KEY_VENDEDOR_NOME, "") ?: ""
        
        return if (codigo != -1 && nome.isNotEmpty()) {
            Vendedor(codigo, nome)
        } else {
            null
        }
    }
    
    fun isLoggedIn(context: Context): Boolean {
        val prefs = getPreferences(context)
        return prefs.getBoolean(KEY_IS_LOGGED_IN, false)
    }
    
    fun logout(context: Context) {
        val prefs = getPreferences(context)
        prefs.edit().clear().apply()
    }
    
    fun getVendedorCodigo(context: Context): Int {
        val prefs = getPreferences(context)
        return prefs.getInt(KEY_VENDEDOR_CODIGO, -1)
    }
    
    fun getVendedorNome(context: Context): String {
        val prefs = getPreferences(context)
        return prefs.getString(KEY_VENDEDOR_NOME, "") ?: ""
    }
}

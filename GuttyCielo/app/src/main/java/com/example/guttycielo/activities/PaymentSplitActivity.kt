package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.guttycielo.R
import com.example.guttycielo.databinding.ActivityPaymentSplitBinding
import com.example.guttycielo.databinding.ItemPaymentSplitBinding
import com.example.guttycielo.models.*
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.ConnectionManager
import com.example.guttycielo.cielo.CieloLioDeepLink
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.util.*

class PaymentSplitActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPaymentSplitBinding
    private var pendingOrder: PendingOrder? = null
    private var paymentSplit: PaymentSplit? = null
    private val currencyFormatter = NumberFormat.getCurrencyInstance(Locale("pt", "BR"))
    private lateinit var cieloLioDeepLink: CieloLioDeepLink
    private var currentPaymentCallback: CieloLioDeepLink.PaymentCallback? = null

    companion object {
        private const val TAG = "PaymentSplitActivity"
        const val EXTRA_PENDING_ORDER = "pending_order"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPaymentSplitBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Verifica se é uma divisão existente (vinda da PaymentSearchActivity)
        val isExistingSplit = intent.getBooleanExtra("existing_split", false)

        if (isExistingSplit) {
            Log.d(TAG, "📋 Divisão existente - criando pedido a partir dos dados do Intent")
            createOrderFromIntent()
        } else {
            // Recebe o pedido pendente (modo normal)
            pendingOrder = intent.getSerializableExtra(EXTRA_PENDING_ORDER) as? PendingOrder

            if (pendingOrder == null) {
                Toast.makeText(this, "Erro: Pedido não encontrado", Toast.LENGTH_LONG).show()
                finish()
                return
            }
        }

        setupViews()
        setupListeners()
        updateOrderInfo()

        // Inicializa Cielo LIO
        cieloLioDeepLink = CieloLioDeepLink(this)

        // Verifica se já existe divisão em andamento para esta comanda
        checkExistingSplit()
    }

    private fun setupViews() {
        binding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    private fun setupListeners() {
        binding.btnCreateSplit.setOnClickListener {
            createPaymentSplit()
        }

        binding.btnCancel.setOnClickListener {
            showCancelDialog()
        }

        binding.btnFinish.setOnClickListener {
            finishPaymentSplit()
        }
    }

    private fun updateOrderInfo() {
        val order = pendingOrder ?: return
        val orderInfo = "Mesa: ${order.tableNumber} | Cliente: ${order.customerName}\n" +
                "Total: ${currencyFormatter.format(order.totalAmount)}"
        binding.tvOrderInfo.text = orderInfo
    }

    private fun createOrderFromIntent() {
        val customerName = intent.getStringExtra("customer_name") ?: ""
        val tableNumber = intent.getStringExtra("table_number") ?: ""
        val totalAmount = intent.getDoubleExtra("total_amount", 0.0)
        val totalItems = intent.getIntExtra("total_items", 0)

        Log.d(TAG, "🔧 Criando pedido mock a partir do Intent:")
        Log.d(TAG, "   - customerName: $customerName")
        Log.d(TAG, "   - tableNumber: $tableNumber")
        Log.d(TAG, "   - totalAmount: $totalAmount")
        Log.d(TAG, "   - totalItems: $totalItems")

        // Cria um pedido mock com os dados do Intent
        pendingOrder = PendingOrder(
            customerName = customerName,
            tableNumber = tableNumber,
            totalAmount = totalAmount,
            totalItems = totalItems,
            items = emptyList() // Para divisão existente, não precisamos dos itens
        )

        Log.d(TAG, "✅ Pedido mock criado com sucesso")
    }

    private fun checkExistingSplit() {
        val order = pendingOrder ?: return
        val comandaId = order.tableNumber.toIntOrNull() ?: 0

        if (comandaId == 0) {
            Log.d(TAG, "⚠️ Comanda ID inválido, não verificando divisão existente")
            return
        }

        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentSplitActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    Log.d(TAG, "🔍 Verificando divisão existente para comanda $comandaId")
                    val existingSplit = DatabaseManager.getExistingSplit(comandaId)

                    runOnUiThread {
                        if (existingSplit != null) {
                            Log.d(TAG, "📋 Divisão existente encontrada - carregando estado anterior")
                            loadExistingSplit(existingSplit)
                        } else {
                            Log.d(TAG, "✨ Nenhuma divisão existente - modo normal")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao verificar divisão existente: ${e.message}")
            }
        }
    }

    private fun createPaymentSplit() {
        val splitCountText = binding.etSplitCount.text.toString()
        if (splitCountText.isEmpty()) {
            Toast.makeText(this, "Digite o número de pessoas", Toast.LENGTH_SHORT).show()
            return
        }

        val splitCount = splitCountText.toIntOrNull()
        if (splitCount == null || splitCount < 2 || splitCount > 10) {
            Toast.makeText(this, "Número de pessoas deve ser entre 2 e 10", Toast.LENGTH_SHORT).show()
            return
        }

        // Primeiro, tenta criar as tabelas se não existirem
        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentSplitActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    // Tenta criar as tabelas
                    val tablesCreated = DatabaseManager.createSplitTables()
                    Log.d(TAG, "Tabelas de divisão: ${if (tablesCreated) "OK" else "Erro"}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao verificar tabelas: ${e.message}")
            }

            // Continua com a criação da divisão
            runOnUiThread {
                createSplitInternal(splitCount)
            }
        }
    }

    private fun createSplitInternal(splitCount: Int) {

        val order = pendingOrder ?: return

        // Cria a divisão de pagamento
        paymentSplit = PaymentSplit(
            comandaId = order.tableNumber.toIntOrNull() ?: 0,
            totalParcelas = splitCount,
            valorTotal = order.totalAmount
        )

        // Cria as parcelas
        val valorPorParcela = order.totalAmount / splitCount
        for (i in 1..splitCount) {
            val parcela = PaymentSplitItem(
                parcelaNumero = i,
                valor = valorPorParcela
            )
            paymentSplit!!.parcelas.add(parcela)
        }

        // Salva a divisão e todas as parcelas no banco imediatamente
        saveInitialSplitToDatabase()

        // Atualiza a interface
        updateSplitInterface()

        // Esconde o card de configuração e mostra o status
        binding.cardSplitConfig.visibility = View.GONE
        binding.cardSplitStatus.visibility = View.VISIBLE

        Log.d(TAG, "Divisão criada: ${splitCount} parcelas de ${currencyFormatter.format(valorPorParcela)}")
    }

    private fun updateSplitInterface() {
        val split = paymentSplit ?: return
        
        // Limpa o container
        binding.containerSplitItems.removeAllViews()
        
        // Adiciona cada parcela
        split.parcelas.forEach { parcela ->
            addSplitItemView(parcela)
        }
        
        // Atualiza o status
        updateSplitStatus()
    }

    private fun addSplitItemView(parcela: PaymentSplitItem) {
        val itemBinding = ItemPaymentSplitBinding.inflate(LayoutInflater.from(this))
        
        // Configura os dados
        itemBinding.tvPersonNumber.text = parcela.parcelaNumero.toString()
        itemBinding.tvPersonTitle.text = "Pessoa ${parcela.parcelaNumero}"
        itemBinding.tvAmount.text = currencyFormatter.format(parcela.valor)
        
        // Configura o status
        updateItemStatus(itemBinding, parcela)
        
        // Configura o clique
        itemBinding.root.setOnClickListener {
            if (parcela.status == PaymentStatus.NAO_PAGO) {
                processPaymentForItem(parcela)
            }
        }
        
        // Configura o botão remover com lógica inteligente
        val split = paymentSplit!!
        val temParcelasPagas = split.parcelas.any { it.status == PaymentStatus.PAGO || it.status == PaymentStatus.DINHEIRO }
        val podeRemover = if (temParcelasPagas) {
            // Se tem parcelas pagas, deve manter pelo menos 2 parcelas
            split.parcelas.size > 2
        } else {
            // Se não tem parcelas pagas, pode remover até ficar 1
            split.parcelas.size > 1
        }

        if (podeRemover) {
            itemBinding.btnRemove.visibility = View.VISIBLE
            itemBinding.btnRemove.setOnClickListener {
                showRemoveItemDialog(parcela)
            }
        } else {
            itemBinding.btnRemove.visibility = View.GONE
        }

        // Configura o botão editar valor (só para parcelas não pagas)
        if (parcela.status == PaymentStatus.NAO_PAGO) {
            itemBinding.btnEditValue.visibility = View.VISIBLE
            itemBinding.btnEditValue.setOnClickListener {
                showEditValueDialog(parcela)
            }
        } else {
            itemBinding.btnEditValue.visibility = View.GONE
        }

        binding.containerSplitItems.addView(itemBinding.root)
    }

    private fun updateItemStatus(itemBinding: ItemPaymentSplitBinding, parcela: PaymentSplitItem) {
        itemBinding.tvStatus.text = parcela.status.displayName
        
        // Define a cor do status
        val color = when (parcela.status) {
            PaymentStatus.NAO_PAGO -> getColor(R.color.gray)
            PaymentStatus.PAGO -> getColor(R.color.light_blue)
            PaymentStatus.DINHEIRO -> getColor(R.color.light_blue)
            PaymentStatus.CANCELADO -> getColor(android.R.color.holo_orange_dark)
            PaymentStatus.RECUSADO -> getColor(android.R.color.holo_red_dark)
        }
        itemBinding.tvStatus.setBackgroundColor(color)
        
        // Mostra a forma de pagamento se pago
        if (parcela.status == PaymentStatus.PAGO || parcela.status == PaymentStatus.DINHEIRO) {
            itemBinding.tvPaymentMethod.text = parcela.formaPagamento?.displayName ?: ""
            itemBinding.tvPaymentMethod.visibility = View.VISIBLE
        } else {
            itemBinding.tvPaymentMethod.visibility = View.GONE
        }
    }

    private fun updateSplitStatus() {
        val split = paymentSplit ?: return
        
        val parcelasPagas = split.parcelas.count { it.status == PaymentStatus.PAGO || it.status == PaymentStatus.DINHEIRO }
        val valorRestante = split.getValorRestante()
        
        val statusText = "$parcelasPagas de ${split.totalParcelas} pagamentos concluídos\n" +
                "FALTA: ${currencyFormatter.format(valorRestante)}"

        binding.tvSplitProgress.text = statusText

        // Atualiza os valores ANTES de verificar se está completa
        split.parcelasPageas = parcelasPagas
        split.valorPago = split.valorTotal - valorRestante

        // Habilita o botão finalizar se todas as parcelas foram pagas
        binding.btnFinish.isEnabled = split.isCompleta()

        Log.d(TAG, "📊 Status atualizado:")
        Log.d(TAG, "   - parcelasPagas: $parcelasPagas")
        Log.d(TAG, "   - totalParcelas: ${split.totalParcelas}")
        Log.d(TAG, "   - isCompleta: ${split.isCompleta()}")
        Log.d(TAG, "   - btnFinish.isEnabled: ${binding.btnFinish.isEnabled}")
    }

    private fun processPaymentForItem(parcela: PaymentSplitItem) {
        // Mostra dialog para selecionar forma de pagamento
        showPaymentMethodDialog(parcela)
    }

    private fun showPaymentMethodDialog(parcela: PaymentSplitItem) {
        val methods = arrayOf("💳 Débito", "💳 Crédito", "📱 PIX", "💵 Dinheiro")
        
        AlertDialog.Builder(this)
            .setTitle("Pessoa ${parcela.parcelaNumero} - ${currencyFormatter.format(parcela.valor)}")
            .setItems(methods) { _, which ->
                val paymentMethod = when (which) {
                    0 -> PaymentMethod.DEBITO
                    1 -> PaymentMethod.CREDITO
                    2 -> PaymentMethod.PIX
                    3 -> PaymentMethod.DINHEIRO
                    else -> PaymentMethod.DEBITO
                }
                
                if (paymentMethod == PaymentMethod.DINHEIRO) {
                    processCashPaymentForItem(parcela, paymentMethod)
                } else {
                    processCardPaymentForItem(parcela, paymentMethod)
                }
            }
            .setNegativeButton("Cancelar", null)
            .show()
    }

    private fun processCashPaymentForItem(parcela: PaymentSplitItem, paymentMethod: PaymentMethod) {
        AlertDialog.Builder(this)
            .setTitle("💵 Pagamento em Dinheiro")
            .setMessage("DESEJA CONFIRMAR O PAGAMENTO EM DINHEIRO?\n\nValor: ${currencyFormatter.format(parcela.valor)}")
            .setPositiveButton("CONFIRMAR") { _, _ ->
                // Marca como pago em dinheiro
                parcela.status = PaymentStatus.DINHEIRO
                parcela.formaPagamento = paymentMethod
                parcela.dataPagamento = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())

                Log.d(TAG, "💵 Pagamento em dinheiro confirmado:")
                Log.d(TAG, "   - Parcela: ${parcela.parcelaNumero}")
                Log.d(TAG, "   - Status: ${parcela.status}")
                Log.d(TAG, "   - Forma: ${parcela.formaPagamento?.displayName}")

                // Salva a parcela imediatamente no banco
                saveSplitItemToDatabase(parcela)

                // Atualiza a interface
                updateSplitInterface()

                Toast.makeText(this, "💵 Pagamento em dinheiro confirmado!", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("CANCELAR", null)
            .show()
    }

    private fun processCardPaymentForItem(parcela: PaymentSplitItem, paymentMethod: PaymentMethod) {
        val order = pendingOrder ?: return

        // Cria um item fictício para o pagamento individual
        val cartItems = listOf(
            CartItem(
                product = Product(
                    codigoInterno = 0L,
                    codigoGtin = "SPLIT_${parcela.parcelaNumero}",
                    descricao = "Parcela ${parcela.parcelaNumero} - ${order.customerName}",
                    descricaoDetalhada = "Divisão de pagamento",
                    grupo = "Pagamento",
                    categoria = "Pagamento",
                    precoVenda = parcela.valor,
                    qtde = 0.0
                ),
                quantidade = 1
            )
        )

        val orderReference = "SPLIT_${System.currentTimeMillis()}_MESA_${order.tableNumber}_P${parcela.parcelaNumero}"

        // Salva dados da parcela para usar após o pagamento
        val sharedPrefs = getSharedPreferences("split_payment_data", MODE_PRIVATE)
        sharedPrefs.edit().apply {
            putInt("parcela_numero", parcela.parcelaNumero)
            putString("order_reference", orderReference)
            putString("payment_method", paymentMethod.code)
            putFloat("parcela_valor", parcela.valor.toFloat())
            putInt("table_number", order.tableNumber.toIntOrNull() ?: 0)
            putString("customer_name", order.customerName)
            apply()
        }

        Log.d(TAG, "💾 Dados salvos no SharedPreferences:")
        Log.d(TAG, "   - order_reference: $orderReference")
        Log.d(TAG, "   - table_number: ${order.tableNumber}")
        Log.d(TAG, "   - customer_name: ${order.customerName}")
        Log.d(TAG, "   - parcela_numero: ${parcela.parcelaNumero}")

        // Cria callback para o pagamento individual (não abre PaymentResultActivity)
        val callback = object : CieloLioDeepLink.PaymentCallback {
            override fun onPaymentStart() {
                Log.d(TAG, "💳 Pagamento da parcela ${parcela.parcelaNumero} iniciado")
                runOnUiThread {
                    Toast.makeText(this@PaymentSplitActivity, "Processando pagamento da parcela ${parcela.parcelaNumero}...", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onPaymentSuccess(transactionId: String, amount: Long) {
                Log.d(TAG, "✅ Pagamento da parcela ${parcela.parcelaNumero} aprovado: $transactionId")

                runOnUiThread {
                    // Marca a parcela como paga
                    parcela.status = PaymentStatus.PAGO
                    parcela.formaPagamento = paymentMethod
                    parcela.cieloResponse = transactionId
                    parcela.dataPagamento = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())

                    // Atualiza a interface
                    updateSplitInterface()

                    Toast.makeText(this@PaymentSplitActivity, "✅ Parcela ${parcela.parcelaNumero} paga com sucesso!", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onPaymentCancel() {
                Log.d(TAG, "❌ Pagamento da parcela ${parcela.parcelaNumero} cancelado")

                runOnUiThread {
                    parcela.status = PaymentStatus.CANCELADO
                    parcela.dataPagamento = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
                    updateSplitInterface()
                    Toast.makeText(this@PaymentSplitActivity, "❌ Pagamento da parcela ${parcela.parcelaNumero} cancelado", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onPaymentError(error: String) {
                Log.e(TAG, "❌ Erro no pagamento da parcela ${parcela.parcelaNumero}: $error")

                runOnUiThread {
                    parcela.status = PaymentStatus.RECUSADO
                    parcela.dataPagamento = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
                    updateSplitInterface()
                    Toast.makeText(this@PaymentSplitActivity, "❌ Erro na parcela ${parcela.parcelaNumero}: $error", Toast.LENGTH_LONG).show()
                }
            }
        }

        // Salva o callback para usar quando o resultado chegar
        currentPaymentCallback = callback

        // Inicia o pagamento via Deep Link
        cieloLioDeepLink.startPayment(
            cartItems = cartItems,
            orderReference = orderReference,
            paymentCode = paymentMethod.code,
            customerName = order.customerName,
            tableNumber = order.tableNumber.toIntOrNull() ?: 1,
            observation = "Parcela ${parcela.parcelaNumero} de ${paymentSplit?.totalParcelas}",
            callback = callback
        )
    }

    private fun showEditValueDialog(parcela: PaymentSplitItem) {
        val split = paymentSplit ?: return
        val order = pendingOrder ?: return

        // Calcula o valor máximo que esta parcela pode ter
        val valorJaPago = split.parcelas
            .filter { it.status == PaymentStatus.PAGO || it.status == PaymentStatus.DINHEIRO }
            .sumOf { it.valor }
        val valorRestante = order.totalAmount - valorJaPago
        val valorMaximo = valorRestante

        Log.d(TAG, "💰 Editando valor da parcela ${parcela.parcelaNumero}:")
        Log.d(TAG, "   - Valor atual: ${parcela.valor}")
        Log.d(TAG, "   - Valor já pago: $valorJaPago")
        Log.d(TAG, "   - Valor restante: $valorRestante")
        Log.d(TAG, "   - Valor máximo permitido: $valorMaximo")

        // Cria o dialog com input
        val input = android.widget.EditText(this).apply {
            inputType = android.text.InputType.TYPE_CLASS_NUMBER or android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL
            setText(String.format("%.2f", parcela.valor))
            selectAll()
        }

        AlertDialog.Builder(this)
            .setTitle("💰 Editar Valor - Pessoa ${parcela.parcelaNumero}")
            .setMessage("Valor atual: ${currencyFormatter.format(parcela.valor)}\n\nValor máximo: ${currencyFormatter.format(valorMaximo)}\n\nDigite o novo valor:")
            .setView(input)
            .setPositiveButton("✅ CONFIRMAR") { _, _ ->
                val novoValorText = input.text.toString().replace(",", ".")
                val novoValor = novoValorText.toDoubleOrNull()

                if (novoValor == null || novoValor <= 0) {
                    Toast.makeText(this, "❌ Valor inválido", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                if (novoValor > valorMaximo) {
                    Toast.makeText(this, "❌ Valor excede o máximo permitido: ${currencyFormatter.format(valorMaximo)}", Toast.LENGTH_LONG).show()
                    return@setPositiveButton
                }

                editParcelaValue(parcela, novoValor)
            }
            .setNegativeButton("❌ CANCELAR", null)
            .show()
    }

    private fun editParcelaValue(parcela: PaymentSplitItem, novoValor: Double) {
        val split = paymentSplit ?: return
        val order = pendingOrder ?: return

        val valorAntigo = parcela.valor

        Log.d(TAG, "🔄 Editando valor da parcela ${parcela.parcelaNumero}:")
        Log.d(TAG, "   - Valor antigo: $valorAntigo")
        Log.d(TAG, "   - Valor novo: $novoValor")

        // Atualiza o valor da parcela
        parcela.valor = novoValor

        // Recalcula as outras parcelas não pagas
        recalcularParcelasAposEdicao(parcela)

        // Salva no banco
        updateSplitInDatabase()

        // Atualiza a interface
        updateSplitInterface()

        Toast.makeText(this, "✅ Valor atualizado! Parcela ${parcela.parcelaNumero}: ${currencyFormatter.format(novoValor)}", Toast.LENGTH_SHORT).show()
    }

    private fun recalcularParcelasAposEdicao(parcelaEditada: PaymentSplitItem) {
        val split = paymentSplit ?: return
        val order = pendingOrder ?: return

        // Calcula o valor já comprometido (parcelas pagas + parcela editada)
        val valorJaPago = split.parcelas
            .filter { it.status == PaymentStatus.PAGO || it.status == PaymentStatus.DINHEIRO }
            .sumOf { it.valor }
        val valorComprometido = valorJaPago + parcelaEditada.valor

        // Calcula o valor restante para as outras parcelas não pagas
        val valorRestante = order.totalAmount - valorComprometido

        // Pega as parcelas não pagas (exceto a que foi editada)
        val outrasParcelasNaoPagas = split.parcelas.filter {
            it.status == PaymentStatus.NAO_PAGO && it.parcelaNumero != parcelaEditada.parcelaNumero
        }

        Log.d(TAG, "🔄 RECALCULANDO APÓS EDIÇÃO:")
        Log.d(TAG, "   - Valor total da comanda: ${order.totalAmount}")
        Log.d(TAG, "   - Valor já pago: $valorJaPago")
        Log.d(TAG, "   - Valor da parcela editada: ${parcelaEditada.valor}")
        Log.d(TAG, "   - Valor comprometido: $valorComprometido")
        Log.d(TAG, "   - Valor restante: $valorRestante")
        Log.d(TAG, "   - Outras parcelas não pagas: ${outrasParcelasNaoPagas.size}")

        if (outrasParcelasNaoPagas.isNotEmpty()) {
            val valorPorParcela = valorRestante / outrasParcelasNaoPagas.size

            outrasParcelasNaoPagas.forEach { parcela ->
                Log.d(TAG, "   - Atualizando parcela ${parcela.parcelaNumero}: ${parcela.valor} → $valorPorParcela")
                parcela.valor = valorPorParcela
            }
        }
    }

    private fun showRemoveItemDialog(parcela: PaymentSplitItem) {
        val split = paymentSplit!!
        val temParcelasPagas = split.parcelas.any { it.status == PaymentStatus.PAGO || it.status == PaymentStatus.DINHEIRO }

        // Verifica se pode remover esta parcela específica
        val parcelaPaga = parcela.status == PaymentStatus.PAGO || parcela.status == PaymentStatus.DINHEIRO

        if (parcelaPaga) {
            // Não pode remover parcela já paga
            AlertDialog.Builder(this)
                .setTitle("❌ Não é Possível Remover")
                .setMessage("Não é possível remover a parcela ${parcela.parcelaNumero} pois ela já foi paga.\n\nValor pago: ${currencyFormatter.format(parcela.valor)}\nForma: ${parcela.formaPagamento?.displayName ?: "N/A"}")
                .setPositiveButton("OK", null)
                .show()
            return
        }

        // Verifica se pode remover considerando as regras
        val podeRemover = if (temParcelasPagas) {
            // Se tem parcelas pagas, deve manter pelo menos 2 parcelas
            split.parcelas.size > 2
        } else {
            // Se não tem parcelas pagas, pode remover até ficar 1
            split.parcelas.size > 1
        }

        if (!podeRemover) {
            val mensagem = if (temParcelasPagas) {
                "Não é possível remover esta parcela.\n\n⚠️ Como existem parcelas já pagas, você deve manter pelo menos 2 parcelas na divisão (uma paga + uma não paga).\n\nSe quiser alterar a divisão, finalize o pagamento atual primeiro."
            } else {
                "Não é possível remover esta parcela pois é a última restante.\n\nSe quiser cancelar a divisão, saia desta tela e escolha 'Cancelar Divisão' no menu."
            }

            AlertDialog.Builder(this)
                .setTitle("❌ Não é Possível Remover")
                .setMessage(mensagem)
                .setPositiveButton("OK", null)
                .show()
            return
        }

        val message = if (temParcelasPagas) {
            "Deseja remover a parcela ${parcela.parcelaNumero} de ${currencyFormatter.format(parcela.valor)}?\n\n⚠️ ATENÇÃO: Como existem parcelas já pagas, os valores das parcelas restantes NÃO PAGAS serão recalculados, mas as parcelas pagas manterão seus valores originais."
        } else {
            "Deseja remover a parcela ${parcela.parcelaNumero} de ${currencyFormatter.format(parcela.valor)}?\n\nOs valores das outras parcelas serão recalculados automaticamente."
        }

        AlertDialog.Builder(this)
            .setTitle("🗑️ Remover Parcela")
            .setMessage(message)
            .setPositiveButton("✅ SIM, REMOVER") { _, _ ->
                removePaymentItem(parcela)
            }
            .setNegativeButton("❌ CANCELAR", null)
            .show()
    }

    private fun removePaymentItem(parcela: PaymentSplitItem) {
        val split = paymentSplit ?: return
        val order = pendingOrder ?: return

        Log.d(TAG, "🗑️ Removendo parcela ${parcela.parcelaNumero} e recalculando")

        // Remove a parcela
        split.parcelas.remove(parcela)
        split.totalParcelas = split.parcelas.size

        // Recalcula os valores
        split.recalcularParcelas()

        // Verifica se deve cancelar divisão automaticamente
        val temParcelasPagas = split.parcelas.any { it.status == PaymentStatus.PAGO || it.status == PaymentStatus.DINHEIRO }
        val ficouUmaParcela = split.parcelas.size == 1
        val valorIgualOriginal = split.parcelas.firstOrNull()?.valor == order.totalAmount

        Log.d(TAG, "🔍 Verificando condições para cancelamento automático:")
        Log.d(TAG, "   - ficouUmaParcela: $ficouUmaParcela (${split.parcelas.size} parcelas)")
        Log.d(TAG, "   - temParcelasPagas: $temParcelasPagas")
        Log.d(TAG, "   - valorIgualOriginal: $valorIgualOriginal (parcela: ${split.parcelas.firstOrNull()?.valor}, original: ${order.totalAmount})")

        if (ficouUmaParcela && !temParcelasPagas && valorIgualOriginal) {
            Log.d(TAG, "🎯 Detectado cancelamento automático - EXECUTANDO AUTOMATICAMENTE")

            autoCancelSplitAndExit()
            return
        } else {
            Log.d(TAG, "🔄 Condições não atendidas - continuando com divisão normal")
        }

        // Atualiza o banco com as parcelas recalculadas
        updateSplitInDatabase()

        // Atualiza a interface
        updateSplitInterface()

        Toast.makeText(this, "Parcela removida e valores recalculados", Toast.LENGTH_SHORT).show()
    }

    private fun showCancelDialog() {
        val split = paymentSplit
        if (split == null) {
            finish()
            return
        }
        
        val parcelasPagas = split.parcelas.count { it.status == PaymentStatus.PAGO || it.status == PaymentStatus.DINHEIRO }
        
        if (parcelasPagas == 0) {
            AlertDialog.Builder(this)
                .setTitle("Cancelar Divisão")
                .setMessage("Deseja cancelar a divisão de pagamento?")
                .setPositiveButton("SIM") { _, _ -> finish() }
                .setNegativeButton("NÃO", null)
                .show()
        } else {
            val valorRestante = split.getValorRestante()
            val parcelasRestantes = split.totalParcelas - parcelasPagas
            
            AlertDialog.Builder(this)
                .setTitle("Cancelar Divisão")
                .setMessage("Deseja cancelar? Ainda restam $parcelasRestantes parcelas de ${currencyFormatter.format(valorRestante / parcelasRestantes)} a serem pagas.")
                .setPositiveButton("SIM") { _, _ -> finish() }
                .setNegativeButton("NÃO", null)
                .show()
        }
    }

    private fun finishPaymentSplit() {
        val split = paymentSplit ?: return
        val order = pendingOrder ?: return

        if (!split.isCompleta()) {
            Toast.makeText(this, "Nem todas as parcelas foram pagas", Toast.LENGTH_SHORT).show()
            return
        }

        // Desabilita botão para evitar duplo clique
        binding.btnFinish.isEnabled = false
        binding.btnFinish.text = "FINALIZANDO..."

        lifecycleScope.launch {
            try {
                // Recarrega a conexão
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentSplitActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    // Salva a divisão no banco
                    val success = saveSplitPaymentToDatabase(split, order)

                    runOnUiThread {
                        if (success) {
                            Toast.makeText(this@PaymentSplitActivity, "Divisão de pagamento concluída!", Toast.LENGTH_SHORT).show()

                            // Volta para a tela principal
                            val intent = Intent(this@PaymentSplitActivity, PaymentSearchActivity::class.java).apply {
                                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
                            }
                            startActivity(intent)
                            finish()
                        } else {
                            binding.btnFinish.isEnabled = true
                            binding.btnFinish.text = "FINALIZAR"
                            Toast.makeText(this@PaymentSplitActivity, "Erro ao finalizar divisão de pagamento", Toast.LENGTH_LONG).show()
                        }
                    }
                } else {
                    runOnUiThread {
                        binding.btnFinish.isEnabled = true
                        binding.btnFinish.text = "FINALIZAR"
                        Toast.makeText(this@PaymentSplitActivity, "Erro: Conexão não configurada", Toast.LENGTH_LONG).show()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao finalizar divisão: ${e.message}", e)
                runOnUiThread {
                    binding.btnFinish.isEnabled = true
                    binding.btnFinish.text = "FINALIZAR"
                    Toast.makeText(this@PaymentSplitActivity, "Erro ao finalizar: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private suspend fun saveSplitPaymentToDatabase(split: PaymentSplit, order: PendingOrder): Boolean {
        return try {
            // 1. Salva a divisão principal
            val splitSaved = DatabaseManager.saveSplitPayment(split)
            if (!splitSaved) {
                Log.e(TAG, "Erro ao salvar divisão principal")
                return false
            }

            // 2. Salva cada parcela
            for (parcela in split.parcelas) {
                val parcelaSaved = DatabaseManager.saveSplitPaymentItem(split.comandaId, parcela)
                if (!parcelaSaved) {
                    Log.e(TAG, "Erro ao salvar parcela ${parcela.parcelaNumero}")
                    return false
                }
            }

            // 3. Marca o pedido como pago com STATUS = 7 (DIVISÃO)
            // Para divisão de pagamento, sempre usa status 7, independente das formas de pagamento
            val statusFinal = 7 // Status fixo para divisão

            Log.d(TAG, "🎯 Determinando status final para pedidos_terminal:")
            Log.d(TAG, "   - Tipo: DIVISÃO DE PAGAMENTO")
            Log.d(TAG, "   - Status final: $statusFinal (DIVISÃO)")
            Log.d(TAG, "   - Total de parcelas: ${split.parcelas.size}")
            Log.d(TAG, "   - Formas de pagamento utilizadas:")

            split.parcelas.forEachIndexed { index, parcela ->
                if (parcela.status == PaymentStatus.PAGO || parcela.status == PaymentStatus.DINHEIRO) {
                    Log.d(TAG, "     • Parcela ${parcela.parcelaNumero}: R$ ${String.format("%.2f", parcela.valor)} - ${parcela.formaPagamento?.displayName ?: "N/A"}")
                }
            }

            val orderPaid = DatabaseManager.markOrderAsPaidWithStatus(
                tableNumber = order.tableNumber,
                customerName = order.customerName,
                transactionId = "SPLIT_${System.currentTimeMillis()}",
                status = statusFinal
            )

            if (!orderPaid) {
                Log.e(TAG, "Erro ao marcar pedido como pago")
                return false
            }

            Log.d(TAG, "✅ Divisão de pagamento salva com sucesso")
            true

        } catch (e: Exception) {
            Log.e(TAG, "Erro ao salvar divisão no banco: ${e.message}", e)
            false
        }
    }

    private fun processPaymentResult(status: String, transactionId: String, amount: Long, errorMessage: String, parcelaNumero: Int, paymentMethodCode: String) {
        Log.d(TAG, "🔄 Processando resultado real do pagamento:")
        Log.d(TAG, "   - status: $status")
        Log.d(TAG, "   - transactionId: $transactionId")
        Log.d(TAG, "   - amount: $amount")
        Log.d(TAG, "   - parcelaNumero: $parcelaNumero")
        Log.d(TAG, "   - paymentMethodCode: $paymentMethodCode")

        if (parcelaNumero == -1) {
            Log.e(TAG, "❌ Número da parcela não encontrado nos dados salvos")
            return
        }

        // Encontra a parcela correspondente
        val split = paymentSplit ?: return
        val parcela = split.parcelas.find { it.parcelaNumero == parcelaNumero }

        if (parcela == null) {
            Log.e(TAG, "❌ Parcela $parcelaNumero não encontrada")
            return
        }

        // Determina a forma de pagamento
        val paymentMethod = when (paymentMethodCode) {
            "DEBITO_AVISTA" -> PaymentMethod.DEBITO
            "CREDITO_AVISTA" -> PaymentMethod.CREDITO
            "PIX" -> PaymentMethod.PIX
            else -> PaymentMethod.DEBITO
        }

        // Atualiza o status da parcela baseado no resultado real
        when (status) {
            "success" -> {
                Log.d(TAG, "✅ Marcando parcela $parcelaNumero como PAGA")
                parcela.status = PaymentStatus.PAGO
                parcela.formaPagamento = paymentMethod
                parcela.cieloResponse = transactionId
                parcela.dataPagamento = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())

                Toast.makeText(this, "✅ Parcela $parcelaNumero paga com sucesso!", Toast.LENGTH_SHORT).show()
            }
            "cancel" -> {
                Log.d(TAG, "❌ Marcando parcela $parcelaNumero como CANCELADA")
                parcela.status = PaymentStatus.CANCELADO
                parcela.dataPagamento = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())

                Toast.makeText(this, "❌ Pagamento da parcela $parcelaNumero cancelado", Toast.LENGTH_SHORT).show()
            }
            "error" -> {
                Log.d(TAG, "❌ Marcando parcela $parcelaNumero como RECUSADA")
                parcela.status = PaymentStatus.RECUSADO
                parcela.dataPagamento = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())

                Toast.makeText(this, "❌ Erro na parcela $parcelaNumero: ${errorMessage.ifEmpty { "Erro desconhecido" }}", Toast.LENGTH_LONG).show()
            }
        }

        // Limpa os dados da parcela após processamento
        val sharedPrefs = getSharedPreferences("split_payment_data", MODE_PRIVATE)
        sharedPrefs.edit().clear().apply()
        Log.d(TAG, "🧹 Dados da parcela limpos após processamento")

        // Salva a parcela imediatamente no banco
        saveSplitItemToDatabase(parcela)

        // Atualiza a interface
        updateSplitInterface()
    }

    private fun saveInitialSplitToDatabase() {
        val split = paymentSplit ?: return
        val order = pendingOrder ?: return
        val comandaId = order.tableNumber.toIntOrNull() ?: 0

        if (comandaId == 0) {
            Log.e(TAG, "❌ Comanda ID inválido para salvamento inicial")
            return
        }

        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentSplitActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    Log.d(TAG, "💾 Salvando divisão inicial e todas as parcelas no banco...")

                    // Salva todas as parcelas (inicialmente com status PENDENTE)
                    for (parcela in split.parcelas) {
                        val success = DatabaseManager.saveSplitItemImmediate(
                            comandaId = comandaId,
                            parcela = parcela,
                            totalParcelas = split.totalParcelas,
                            valorTotal = split.valorTotal
                        )

                        if (success) {
                            Log.d(TAG, "✅ Parcela ${parcela.parcelaNumero} salva no banco (PENDENTE)")
                        } else {
                            Log.e(TAG, "❌ Erro ao salvar parcela ${parcela.parcelaNumero} no banco")
                        }
                    }

                    runOnUiThread {
                        Log.d(TAG, "✅ Divisão inicial salva no banco com todas as parcelas")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao salvar divisão inicial no banco: ${e.message}")
            }
        }
    }

    private fun updateSplitInDatabase() {
        val split = paymentSplit ?: return
        val order = pendingOrder ?: return
        val comandaId = order.tableNumber.toIntOrNull() ?: 0

        if (comandaId == 0) {
            Log.e(TAG, "❌ Comanda ID inválido para atualização")
            return
        }

        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentSplitActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    Log.d(TAG, "🔄 Atualizando todas as parcelas no banco após recálculo...")

                    // Primeiro, deleta todas as parcelas antigas desta comanda
                    val deleteSuccess = DatabaseManager.deleteSplitItems(comandaId)

                    if (deleteSuccess) {
                        Log.d(TAG, "✅ Parcelas antigas deletadas do banco")

                        // Depois, salva todas as parcelas recalculadas
                        for (parcela in split.parcelas) {
                            val success = DatabaseManager.saveSplitItemImmediate(
                                comandaId = comandaId,
                                parcela = parcela,
                                totalParcelas = split.totalParcelas,
                                valorTotal = split.valorTotal
                            )

                            if (success) {
                                Log.d(TAG, "✅ Parcela ${parcela.parcelaNumero} recalculada salva no banco")
                            } else {
                                Log.e(TAG, "❌ Erro ao salvar parcela ${parcela.parcelaNumero} recalculada")
                            }
                        }

                        runOnUiThread {
                            Log.d(TAG, "✅ Todas as parcelas atualizadas no banco com valores recalculados")
                        }
                    } else {
                        Log.e(TAG, "❌ Erro ao deletar parcelas antigas do banco")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao atualizar divisão no banco: ${e.message}")
            }
        }
    }

    private fun saveSplitItemToDatabase(parcela: PaymentSplitItem) {
        val split = paymentSplit ?: return
        val order = pendingOrder ?: return
        val comandaId = order.tableNumber.toIntOrNull() ?: 0

        if (comandaId == 0) {
            Log.e(TAG, "❌ Comanda ID inválido para salvamento")
            return
        }

        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentSplitActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    Log.d(TAG, "💾 Atualizando parcela ${parcela.parcelaNumero} no banco...")
                    val success = DatabaseManager.saveSplitItemImmediate(
                        comandaId = comandaId,
                        parcela = parcela,
                        totalParcelas = split.totalParcelas,
                        valorTotal = split.valorTotal
                    )

                    runOnUiThread {
                        if (success) {
                            Log.d(TAG, "✅ Parcela ${parcela.parcelaNumero} atualizada no banco com sucesso")
                        } else {
                            Log.e(TAG, "❌ Erro ao atualizar parcela ${parcela.parcelaNumero} no banco")
                            Toast.makeText(this@PaymentSplitActivity, "⚠️ Erro ao salvar no banco, mas pagamento foi processado", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro ao atualizar parcela no banco: ${e.message}")
            }
        }
    }

    private fun loadExistingSplit(existingSplit: PaymentSplit) {
        Log.d(TAG, "🔄 Carregando divisão existente:")
        Log.d(TAG, "   - Total parcelas: ${existingSplit.totalParcelas}")
        Log.d(TAG, "   - Parcelas pagas: ${existingSplit.parcelasPageas}")
        Log.d(TAG, "   - Valor total: ${currencyFormatter.format(existingSplit.valorTotal)}")

        // Define a divisão atual
        paymentSplit = existingSplit

        // Esconde o card de configuração e mostra o status
        binding.cardSplitConfig.visibility = View.GONE
        binding.cardSplitStatus.visibility = View.VISIBLE

        // Atualiza a interface
        updateSplitInterface()

        // Mostra mensagem para o usuário
        Toast.makeText(this, "📋 Divisão em andamento carregada!\n${existingSplit.parcelasPageas} de ${existingSplit.totalParcelas} parcelas pagas", Toast.LENGTH_LONG).show()
    }



    private fun autoCancelSplitAndExit() {
        val order = pendingOrder ?: return
        val comandaId = order.tableNumber.toIntOrNull() ?: 0

        if (comandaId == 0) {
            Log.e(TAG, "⚠️ Comanda ID inválido para cancelamento automático")
            finish()
            return
        }

        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentSplitActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    Log.d(TAG, "🎯 Executando cancelamento automático da divisão")
                    val success = DatabaseManager.deleteSplitItems(comandaId)

                    runOnUiThread {
                        if (success) {
                            Log.d(TAG, "✅ Divisão cancelada automaticamente com sucesso")
                            Toast.makeText(this@PaymentSplitActivity, "🎯 Divisão cancelada automaticamente\nRestou apenas 1 parcela = valor total", Toast.LENGTH_LONG).show()
                        } else {
                            Log.e(TAG, "❌ Erro no cancelamento automático")
                            Toast.makeText(this@PaymentSplitActivity, "⚠️ Erro no cancelamento automático", Toast.LENGTH_SHORT).show()
                        }

                        // Volta para a tela anterior independente do resultado
                        finish()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Erro no cancelamento automático: ${e.message}")
                runOnUiThread {
                    Toast.makeText(this@PaymentSplitActivity, "⚠️ Erro no cancelamento automático", Toast.LENGTH_SHORT).show()
                    finish()
                }
            }
        }
    }

    override fun onBackPressed() {
        showCancelDialog()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        Log.d(TAG, "onActivityResult - requestCode: $requestCode, resultCode: $resultCode")
        Log.d(TAG, "⚠️ IGNORANDO onActivityResult para divisão - aguardando callback real da PaymentCallbackActivity")

        // Para divisões de pagamento, NÃO processamos o onActivityResult
        // O resultado real vem via PaymentCallbackActivity que processa o JSON completo
        // O onActivityResult só retorna resultCode=0 (cancelado) mesmo quando o pagamento é aprovado
    }

    override fun onResume() {
        super.onResume()

        // Verifica se há resultado de pagamento da PaymentCallbackActivity
        val splitResultPrefs = getSharedPreferences("split_payment_result", MODE_PRIVATE)
        val resultStatus = splitResultPrefs.getString("status", "") ?: ""
        val resultTimestamp = splitResultPrefs.getLong("timestamp", 0)

        if (resultStatus.isNotEmpty() && resultTimestamp > 0) {
            Log.d(TAG, "📥 Resultado de pagamento recebido da PaymentCallbackActivity")

            val transactionId = splitResultPrefs.getString("transaction_id", "") ?: ""
            val amount = splitResultPrefs.getLong("amount", 0)
            val errorMessage = splitResultPrefs.getString("error_message", "") ?: ""
            val parcelaNumero = splitResultPrefs.getInt("parcela_numero", -1)
            val paymentMethodCode = splitResultPrefs.getString("payment_method", "") ?: ""

            // Limpa o resultado
            splitResultPrefs.edit().clear().apply()

            // Processa o resultado real
            processPaymentResult(resultStatus, transactionId, amount, errorMessage, parcelaNumero, paymentMethodCode)
        }

        // Verifica se há dados de pagamento salvos (resultado de parcela) - LEGACY
        val sharedPrefs = getSharedPreferences("split_payment_data", MODE_PRIVATE)
        val parcelaNumero = sharedPrefs.getInt("parcela_numero", -1)

        if (parcelaNumero != -1) {
            Log.d(TAG, "Dados de parcela ainda presentes: $parcelaNumero (aguardando processamento)")
            // NÃO limpa os dados aqui - eles serão limpos após o processamento
        }
    }
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/light_blue"
        app:title="Realizar Pagamento"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white" />

    <!-- <PERSON><PERSON> de busca -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🔍 Buscar Pedidos em Aberto"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginBottom="16dp" />

            <!-- Campo nome do cliente -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:hint="Nome do Cliente"
                app:startIconDrawable="@android:drawable/ic_menu_search">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etCustomerName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPersonName|textCapWords"
                    android:gravity="center"
                    />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Campo número da comanda -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:hint="Número da Comanda/Mesa"
                app:startIconDrawable="@android:drawable/ic_menu_info_details">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etTableNumber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:gravity="center"
                    />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Botão Unir Comandas -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnUnirComandas"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="3dp"
                android:text="UNIR COMANDAS"
                android:textSize="14sp"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:textColor="@color/white"
                android:backgroundTint="@color/orange"
                app:cornerRadius="8dp" />

            <!-- Botões de ação -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSearch"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="BUSCAR"
                    android:backgroundTint="@color/light_blue"
                    app:cornerRadius="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnClearFilters"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="LIMPAR"
                    android:backgroundTint="@color/gray"
                    app:cornerRadius="8dp"
                    android:textColor="@color/white"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Lista de pedidos -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp">

        <!-- RecyclerView para os pedidos -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvPendingOrders"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="16dp" />

        <!-- Indicador de carregamento -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- Mensagem quando não há resultados -->
        <TextView
            android:id="@+id/tvNoResults"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="Nenhum pedido em aberto encontrado"
            android:textSize="16sp"
            android:textColor="@color/gray"
            android:gravity="center"
            android:visibility="gone"
            android:drawableTop="@android:drawable/ic_menu_search"
            android:drawablePadding="16dp"
            android:drawableTint="@color/gray" />

    </FrameLayout>

</LinearLayout>

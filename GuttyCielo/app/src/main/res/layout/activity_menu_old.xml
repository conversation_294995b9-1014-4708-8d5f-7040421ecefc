<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="Gutty - Menu Principal"
        app:titleTextColor="@color/white"
        app:menu="@menu/menu_main"
        android:backgroundTint="@color/light_blue"
        />

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintVertical_bias="1.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="24dp"
            android:gravity="center"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardAnotarPedido"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/blue_clear"
                android:layout_marginBottom="10dp"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:rippleColor="?attr/colorPrimary">

                <LinearLayout
                    android:layout_margin="12dp"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:background="@color/blue_clear"
                    android:gravity="center"
                    android:orientation="vertical"
                    >

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"

                        android:layout_marginBottom="12dp"
                        android:src="@drawable/ic_cart"
                        app:tint="@color/gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ANOTAR PEDIDO"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold" />


                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardReceberPagamento"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:backgroundTint="@color/blue_clear"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:rippleColor="?attr/colorPrimary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="12dp"
                    android:background="@color/blue_clear"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="8dp"
                        android:src="@drawable/ic_money"
                        app:tint="@color/gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:text="RECEBER PAGAMENTO"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold" />


                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardVerComandas"
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:clickable="true"
                android:backgroundTint="@color/blue_clear"
                android:focusable="true"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:rippleColor="?attr/colorPrimary">

                <LinearLayout
                    android:layout_margin="12dp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/blue_clear"
                    android:gravity="center"
                    android:orientation="vertical"
                    >

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginBottom="8dp"
                        android:src="@drawable/ic_products"
                        app:tint="@color/gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="VER COMANDAS"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold" />


                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardPedidosPendentes"
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:layout_marginTop="10dp"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:rippleColor="?attr/colorPrimary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/blue_clear"
                    android:gravity="center"
                    android:orientation="vertical"
                    >

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginBottom="8dp"
                        android:src="@drawable/ic_cart"
                        app:tint="@color/gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="PEDIDOS PENDENTES"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold" />
\

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>

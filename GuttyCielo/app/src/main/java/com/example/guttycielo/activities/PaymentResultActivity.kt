package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.guttycielo.MainActivity
import com.example.guttycielo.databinding.ActivityPaymentResultBinding
import com.example.guttycielo.utils.ConnectionManager
import com.example.guttycielo.utils.CartManager
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import kotlinx.coroutines.launch

/**
 * Activity para mostrar o resultado do pagamento da Cielo LIO
 */
class PaymentResultActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPaymentResultBinding
    private var countDownTimer: CountDownTimer? = null

    companion object {
        const val EXTRA_PAYMENT_STATUS = "payment_status"
        const val EXTRA_TRANSACTION_ID = "transaction_id"
        const val EXTRA_AMOUNT = "amount"
        const val EXTRA_ERROR_MESSAGE = "error_message"
        const val EXTRA_TABLE_NUMBER = "table_number"
        const val EXTRA_ORDER_REFERENCE = "order_reference"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPaymentResultBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Recebe os dados do pagamento
        val status = intent.getStringExtra(EXTRA_PAYMENT_STATUS)
        val transactionId = intent.getStringExtra(EXTRA_TRANSACTION_ID) ?: ""
        val amount = intent.getLongExtra(EXTRA_AMOUNT, 0L)
        val errorMessage = intent.getStringExtra(EXTRA_ERROR_MESSAGE)
        val tableNumber = intent.getIntExtra(EXTRA_TABLE_NUMBER, 0)
        val orderReference = intent.getStringExtra(EXTRA_ORDER_REFERENCE) ?: ""
        val paymentMethod = intent.getStringExtra("payment_method") ?: ""

        android.util.Log.d("PaymentResultActivity", "=== RESULTADO DO PAGAMENTO ===")
        android.util.Log.d("PaymentResultActivity", "Status: $status")
        android.util.Log.d("PaymentResultActivity", "Transaction ID: $transactionId")
        android.util.Log.d("PaymentResultActivity", "Amount: $amount")
        android.util.Log.d("PaymentResultActivity", "Error: $errorMessage")

        // Configura a interface baseada no resultado
        setupUI(status, transactionId, amount, errorMessage, tableNumber, orderReference, paymentMethod)

        // Configura os botões
        setupButtons(status, tableNumber, orderReference)
    }

    private fun setupUI(status: String?, transactionId: String, amount: Long, errorMessage: String?, tableNumber: Int, orderReference: String, paymentMethod: String = "") {
        when (status) {
            "success" -> {
                // Verifica se é pagamento em dinheiro
                val isCashPayment = paymentMethod == "DINHEIRO" || transactionId.startsWith("DINHEIRO_")

                if (isCashPayment) {
                    // Pagamento em dinheiro
                    binding.ivResultIcon.setImageResource(com.example.guttycielo.R.drawable.ic_money)
                    binding.tvResultTitle.text = "💵 PAGAMENTO BEM SUCEDIDO"
                    binding.tvResultMessage.text = "O pagamento em dinheiro foi confirmado com sucesso."

                    // Mostra forma de pagamento
                    binding.tvPaymentMethod.text = "FORMA DE PAGAMENTO: DINHEIRO"
                    binding.tvPaymentMethod.visibility = android.view.View.VISIBLE
                } else {
                    // Pagamento com cartão/PIX
                    binding.ivResultIcon.setImageResource(android.R.drawable.ic_dialog_info)
                    binding.tvResultTitle.text = "✅ Pagamento Aprovado!"
                    binding.tvResultMessage.text = "O pagamento foi processado com sucesso."

                    if (transactionId.isNotEmpty()) {
                        binding.tvTransactionDetails.text = "ID da Transação: $transactionId"
                        binding.tvTransactionDetails.visibility = android.view.View.VISIBLE
                    }
                }

                if (amount > 0) {
                    val amountInReais = amount / 100.0
                    val formatter = java.text.NumberFormat.getCurrencyInstance(java.util.Locale("pt", "BR"))
                    binding.tvAmountDetails.text = "Valor: ${formatter.format(amountInReais)}"
                    binding.tvAmountDetails.visibility = android.view.View.VISIBLE
                }

                binding.btnAction.text = "FINALIZAR (4s)"

                // Inicia processo automático para pagamento aprovado
                startAutomaticFinalization(tableNumber, orderReference)
            }
            
            "cancelled" -> {
                // Pagamento cancelado
                binding.ivResultIcon.setImageResource(android.R.drawable.ic_dialog_alert)
                binding.tvResultTitle.text = "❌ Pagamento Cancelado"
                binding.tvResultMessage.text = "O pagamento foi cancelado pelo usuário."

                binding.btnAction.text = "TENTAR NOVAMENTE"
            }

            else -> {
                // Erro no pagamento
                binding.ivResultIcon.setImageResource(android.R.drawable.ic_dialog_alert)
                binding.tvResultTitle.text = "❌ Erro no Pagamento"
                binding.tvResultMessage.text = errorMessage ?: "Ocorreu um erro durante o pagamento."

                binding.btnAction.text = "TENTAR NOVAMENTE"
            }
        }
    }

    private fun startAutomaticFinalization(tableNumber: Int, orderReference: String) {
        // Inicia contador de 4 segundos
        countDownTimer = object : CountDownTimer(4000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val secondsLeft = (millisUntilFinished / 1000).toInt()
                binding.btnAction.text = "FINALIZAR (${secondsLeft}s)"
            }

            override fun onFinish() {
                // Finaliza automaticamente após 4 segundos
                binding.btnAction.text = "FINALIZANDO..."

                // Verifica se é pagamento em dinheiro
                val paymentMethod = intent.getStringExtra("payment_method") ?: ""
                val transactionId = intent.getStringExtra(EXTRA_TRANSACTION_ID) ?: ""
                val isCashPayment = paymentMethod == "DINHEIRO" || transactionId.startsWith("DINHEIRO_")

                if (isCashPayment) {
                    // Para pagamento em dinheiro, só limpa o carrinho e finaliza
                    clearCartAndFinish()
                } else {
                    // Para pagamentos com cartão/PIX, marca como pago no banco
                    markOrderAsPaidAndClearCart(tableNumber, orderReference)
                }
            }
        }.start()
    }

    private fun setupButtons(status: String?, tableNumber: Int, orderReference: String) {
        binding.btnAction.setOnClickListener {
            when (status) {
                "success" -> {
                    // Cancela o timer automático se o usuário clicar
                    countDownTimer?.cancel()
                    binding.btnAction.text = "FINALIZANDO..."

                    // Verifica se é pagamento em dinheiro
                    val paymentMethod = intent.getStringExtra("payment_method") ?: ""
                    val transactionId = intent.getStringExtra(EXTRA_TRANSACTION_ID) ?: ""
                    val isCashPayment = paymentMethod == "DINHEIRO" || transactionId.startsWith("DINHEIRO_")

                    if (isCashPayment) {
                        // Para pagamento em dinheiro, só limpa o carrinho e finaliza
                        // (o banco já foi atualizado na OrderPaymentActivity)
                        clearCartAndFinish()
                    } else {
                        // Para pagamentos com cartão/PIX, marca como pago no banco
                        markOrderAsPaidAndClearCart(tableNumber, orderReference)
                    }
                }
                else -> {
                    // Cancelado ou erro - TENTAR NOVAMENTE (volta para a tela de origem)
                    finishAndGoToMain()
                }
            }
        }
    }

    private fun markOrderAsPaidAndClearCart(tableNumber: Int, orderReference: String) {
        // Desabilita botão para evitar duplo clique
        binding.btnAction.isEnabled = false
        binding.btnAction.text = "PROCESSANDO..."

        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@PaymentResultActivity)
                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)

                    // Recupera o tipo de pagamento salvo
                    val sharedPrefs = getSharedPreferences("payment_data", MODE_PRIVATE)
                    val paymentType = sharedPrefs.getString("payment_type", "DEBITO") ?: "DEBITO"

                    android.util.Log.d("PaymentResultActivity", "🔄 Marcando pedido como pago...")
                    android.util.Log.d("PaymentResultActivity", "Mesa: $tableNumber")
                    android.util.Log.d("PaymentResultActivity", "Referência: $orderReference")
                    android.util.Log.d("PaymentResultActivity", "Tipo de Pagamento: $paymentType")

                    // Marca todos os itens da mesa como pagos com status específico
                    val success = DatabaseManager.markOrderAsPaid(tableNumber, paymentType)

                    if (success) {
                        android.util.Log.d("PaymentResultActivity", "✅ Pedido marcado como pago no banco")

                        // Limpa o carrinho local
                        CartManager.clearCart(this@PaymentResultActivity)
                        android.util.Log.d("PaymentResultActivity", "✅ Carrinho local limpo")

                        runOnUiThread {
                            Toast.makeText(this@PaymentResultActivity, "Pagamento processado com sucesso!", Toast.LENGTH_SHORT).show()
                            finishAndGoToMain()
                        }
                    } else {
                        android.util.Log.e("PaymentResultActivity", "❌ Erro ao marcar pedido como pago")
                        runOnUiThread {
                            Toast.makeText(this@PaymentResultActivity, "Erro ao processar pagamento no banco", Toast.LENGTH_LONG).show()
                            // Reabilita botão
                            binding.btnAction.isEnabled = true
                            binding.btnAction.text = "FINALIZAR"
                        }
                    }

                } else {
                    android.util.Log.e("PaymentResultActivity", "❌ Conexão não configurada")
                    runOnUiThread {
                        Toast.makeText(this@PaymentResultActivity, "Erro: Conexão não configurada", Toast.LENGTH_LONG).show()
                        // Reabilita botão
                        binding.btnAction.isEnabled = true
                        binding.btnAction.text = "FINALIZAR"
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("PaymentResultActivity", "❌ Erro ao processar pagamento: ${e.message}")
                runOnUiThread {
                    Toast.makeText(this@PaymentResultActivity, "Erro ao processar: ${e.message}", Toast.LENGTH_LONG).show()
                    // Reabilita botão
                    binding.btnAction.isEnabled = true
                    binding.btnAction.text = "FINALIZAR"
                }
            }
        }
    }

    private fun clearCartAndFinish() {
        // Limpa o carrinho local
        CartManager.clearCart(this)
        android.util.Log.d("PaymentResultActivity", "✅ Carrinho local limpo (pagamento em dinheiro)")

        runOnUiThread {
            Toast.makeText(this@PaymentResultActivity, "Pagamento em dinheiro processado com sucesso!", Toast.LENGTH_SHORT).show()
            finishAndGoToMain()
        }
    }

    private fun finishAndGoToMain() {
        // Verifica de onde veio o pagamento
        val sharedPrefs = getSharedPreferences("payment_data", MODE_PRIVATE)
        val sourceActivity = sharedPrefs.getString("source_activity", "")

        val intent = if (sourceActivity == "OrderPaymentActivity") {
            // Veio do fluxo de gerenciar pedidos - volta para PaymentSearchActivity
            Intent(this, PaymentSearchActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
            }
        } else {
            // Veio do carrinho ou outro lugar - vai para MainActivity
            Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
            }
        }

        // Limpa os dados de pagamento
        sharedPrefs.edit().clear().apply()

        startActivity(intent)
        finish()
    }



    override fun onBackPressed() {
        // Impede voltar com botão back - força usar os botões da interface
        // super.onBackPressed()
    }

    override fun onDestroy() {
        super.onDestroy()
        // Cancela o timer se a activity for destruída
        countDownTimer?.cancel()
    }
}

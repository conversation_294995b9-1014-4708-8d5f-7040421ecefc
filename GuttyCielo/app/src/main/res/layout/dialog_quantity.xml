<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minWidth="320dp"
    android:padding="24dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Adicionar ao Pedido"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvProductName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        tools:text="COCA-COLA 200ML" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="@color/blue"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvProductName"
        tools:text="R$ 3,50" />

    <TextView
        android:id="@+id/tvQuantityLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="Quantidade:"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPrice" />

    <LinearLayout
        android:id="@+id/layoutQuantity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvQuantityLabel">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnMinus"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:padding="0dp"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            android:insetLeft="0dp"
            android:insetRight="0dp"
            android:text="−"
            android:textSize="24sp"
            android:textStyle="bold"
            app:cornerRadius="8dp"
            android:textColor="@color/light_blue"
            />

        <TextView
            android:id="@+id/tvQuantity"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/black"
            android:textSize="24sp"
            android:textStyle="bold" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnPlus"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:padding="0dp"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            android:insetLeft="0dp"
            android:insetRight="0dp"
            android:text="+"
            android:textSize="24sp"
            android:textStyle="bold"
            app:cornerRadius="8dp"
            android:textColor="@color/light_blue"
            />

    </LinearLayout>

    <TextView
        android:id="@+id/tvTotal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:textColor="@color/blue"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layoutQuantity"
        tools:text="Total: R$ 3,50" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="horizontal"
        android:weightSum="2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTotal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnCancel"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:minWidth="120dp"
            android:text="CANCELAR"
            android:textSize="14sp"
            android:maxLines="1"
            android:singleLine="true"
            android:ellipsize="none"
            app:cornerRadius="8dp"
            android:textColor="@color/blue"
            />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnAdd"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:minWidth="120dp"
            android:text="ADICIONAR"
            android:textSize="14sp"
            android:maxLines="1"
            android:singleLine="true"
            android:ellipsize="none"
            app:cornerRadius="8dp"
            android:backgroundTint="@color/blue"
            />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

package com.example.guttycielo.activities

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.guttycielo.R
import com.example.guttycielo.databinding.ActivityMenuBinding
import com.example.guttycielo.network.DatabaseManager
import com.example.guttycielo.network.NetworkManager
import com.example.guttycielo.utils.ConnectionManager
import com.example.guttycielo.utils.SessionManager
import kotlinx.coroutines.launch

class MenuActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMenuBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMenuBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Verifica se o vendedor está logado
        if (!SessionManager.isLoggedIn(this)) {
            // Se não estiver logado, vai para o login
            val intent = Intent(this, VendedorLoginActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(intent)
            finish()
            return
        }

        setupVendedorInfo()
        setupViews()
    }

    private fun setupVendedorInfo() {
        val vendedor = SessionManager.getVendedor(this)
        if (vendedor != null) {
            binding.tvVendedorInfo.text = "Olá, ${vendedor.nome}"
        }
    }

    private fun setupViews() {
        // Botão de configurações
        binding.btnConfig.setOnClickListener {
            showConfigDialog()
        }

        // Botão de logout
        binding.btnLogout.setOnClickListener {
            showLogoutDialog()
        }

        // ANOTAR PEDIDO - Criar novo pedido
        binding.cardAnotarPedido.setOnClickListener {
            val intent = Intent(this, CategoriesActivity::class.java)
            startActivity(intent)
        }

        // RECEBER PAGAMENTO - Buscar e processar pagamentos
        binding.cardReceberPagamento.setOnClickListener {
            val intent = Intent(this, PaymentSearchActivity::class.java)
            startActivity(intent)
        }

        // VER COMANDAS - Visualizar comandas em aberto (modo somente leitura)
        binding.cardVerComandas.setOnClickListener {
            val intent = Intent(this, PaymentSearchActivity::class.java).apply {
                putExtra("view_only_mode", true) // Modo apenas visualização
            }
            startActivity(intent)
        }

        // PEDIDOS PENDENTES - Pedidos não finalizados salvos localmente
        binding.cardPedidosPendentes.setOnClickListener {
            val intent = Intent(this, OrdersMenuActivity::class.java)
            startActivity(intent)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_main, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_disconnect -> {
                disconnect()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun disconnect() {
        ConnectionManager.clearConnection(this)
        val intent = Intent(this, ConnectionActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    override fun onResume() {
        super.onResume()

        // Recarrega a conexão quando a activity volta ao foco
        lifecycleScope.launch {
            try {
                val (serverUrl, dbConnection) = ConnectionManager.getSavedConnection(this@MenuActivity)

                if (serverUrl != null && dbConnection != null) {
                    NetworkManager.updateBaseUrl(serverUrl)
                    DatabaseManager.setCurrentConnection(dbConnection)
                }
            } catch (e: Exception) {
                // Se houver erro, não faz nada, deixa o usuário tentar navegar
            }
        }
    }

    private fun showConfigDialog() {
        AlertDialog.Builder(this)
            .setTitle("APENAS DESENVOLVEDORES!")
            .setMessage("Deseja alterar as configurações de conexão com o banco de dados? Você perderá acesso ao aplicativo")
            .setPositiveButton("SIM") { _, _ ->
                disconnect()
            }
            .setNegativeButton("CANCELAR", null)
            .show()
    }

    private fun showLogoutDialog() {
        AlertDialog.Builder(this)
            .setTitle("Sair do Sistema")
            .setMessage("Deseja realmente fazer logout?")
            .setPositiveButton("SIM") { _, _ ->
                logout()
            }
            .setNegativeButton("CANCELAR", null)
            .show()
    }

    private fun logout() {
        // Limpa a sessão do vendedor
        SessionManager.logout(this)

        // Vai para o login do vendedor
        val intent = Intent(this, VendedorLoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/light_blue"
        app:title="Unir Comandas"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Card de Merge -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="UNIR COMANDAS"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="16dp" />

                    <!-- Campo Origem -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="Comanda Origem"
                            app:helperText="Comanda que será movida"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etComandaOrigem"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnBuscarOrigem"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:textColor="@color/blue"

                            android:minWidth="0dp"
                            android:minHeight="0dp"
                            android:padding="0dp"
                            android:insetTop="0dp"
                            android:insetBottom="0dp"
                            android:insetLeft="0dp"
                            android:insetRight="0dp"
                            android:text="📋"
                            android:textSize="16sp"
                            app:cornerRadius="8dp"/>

                    </LinearLayout>

                    <!-- Campo Destino -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="Comanda Destino"
                            app:helperText="Comanda que receberá os itens"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etComandaDestino"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="text" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnBuscarDestino"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:textColor="@color/blue"
                            app:cornerRadius="8dp"
                            android:minWidth="0dp"
                            android:minHeight="0dp"
                            android:padding="0dp"
                            android:insetTop="0dp"
                            android:insetBottom="0dp"
                            android:insetLeft="0dp"
                            android:insetRight="0dp"
                            android:text="📋"
                            android:textSize="16sp"
                            />

                    </LinearLayout>

                    <!-- Botões -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="8dp">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnVisualizar"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:text="VISUALIZAR"
                            android:textSize="14sp"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:textColor="@color/blue"
                            app:cornerRadius="8dp" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnUnir"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:text="UNIR"
                            android:textSize="14sp"
                            android:backgroundTint="@color/orange"
                            android:textColor="@color/white"
                            app:cornerRadius="8dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Card de Merges Recentes -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="COMANDAS UNIDAS RECENTEMENTE"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="16dp" />

                    <!-- Lista de merges recentes -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvMergesRecentes"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                    <!-- Mensagem quando não há merges -->
                    <LinearLayout
                        android:id="@+id/tvNoMerges"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="32dp"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📋"
                            android:textSize="48sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Nenhuma comanda unida recente"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Comandas unidas nas últimas 24 horas aparecerão aqui"
                            android:textSize="14sp"
                            android:textColor="@color/gray"
                            android:gravity="center" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
